package com.wosai.upay.job.model;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by lih<PERSON><PERSON> on 2018/7/11.
 */
public class ApolloConfig {

    public static final String CONTRACT_WEIXIN_CONFIG = "contract_weixin_config";  //微信自定义公众号配置
    public static final String CONTRACT_MESSAGE_UNION = "contract_message_union";  //网联银联配置
    public static final String CONTRACT_MESSAGE_UNION_OPEN = "contract_message_union_open";  //银联开放平台配置
    public static final String CONTRACT_MESSAGE_WANMA = "contract_message_wanma";  //万码配置
    public static final String CONTRACT_MESSAGE_LKL = "contract_message_lkl";  //拉卡拉配置
    public static final String CONTRACT_MESSAGE_LKL_PIC = "contract_message_lkl_pic";  //拉卡拉配置



    public static final String CONTRACT_LZ_PARAMS = "contract_lz_params";  //绿洲配置

    public static final String CONTRACT_LZ_CUSTOM_PARAMS = "contract_lz_custom_params";  //绿洲自定义关注公众号配置

    public static final String CONTRACT_LZ_PARAMS_CHANNEL = "channel";
    public static final String CONTRACT_LZ_PARAMS_PROVIDER = "provider";
    public static final String CONTRACT_LZ_PARAMS_APPID = "appid";
    public static final String CONTRACT_LZ_PARAMS_AGENT_NAME = "agent_name";
    public static final String CONTRACT_LZ_PARAMS_MERCHANT_FEE = "merchant_fee";
    public static final String CONTRACT_LZ_PARAMS_MERCHANT_CITY_FEE_RULES = "city_fee_rules";




    public static final String JOB_MERCHANT_CONFIG_PAYWAY_PROVIDER = "job_merchant_config_payway_provider";  //商户交易参数key获取




    public static final String ADD_WEIXIN_CONFIG = "add_weixin_config";  //支付目录配置
    public static final String AUTH_PATH = "auth_path";  //支付目录配置  key




    public static final String APP_MODULE_WHITE_TYPE = "app_module_white_type";  //app_module_white_type 白名单类型


    public static final String CONTRACT_ERROR_MESSAGE = "contract_error_message";  //contract_error_message 报备失败
    public static final String ERROR_KEY = "error_key";
    public static final String ERROR_VALUE = "error_value";


    public static final String WEIXIN_CONFIG_ERROR_MESSAGE = "weixin_config_error_message";  //contract_error_message 微信配置失败



    public static final String WEIXIN_CONFIG_TIME_OUT = "weixin_config_time_out";  //weixin_config_time_out 微信配置sleep


    public static final String DOC = "doc";  //doc配置
    public static final String FUNCTION_NAME = "function_name";
    public static final String FUNCTION_TITLE = "function_title";
    public static final String SERVICE_CHARGE = "service_charge";
    public static final String MARKED = "marked";
    public static final String DREDGE = "dredge";
    public static final String CANCEL = "cancel";
    public static final String CANCEL_SUCCESS = "cancel_success";
    public static final String CANCEL_FAILED = "cancel_failed";


    public static final String APPID_OPEN_NOTICE_SUCCESS_MESSAGE = "appid_open_notice_success_message";  //appid_open_notice_success_message
    public static final String APPID_OPEN_NOTICE_FAIL_MESSAGE = "appid_open_notice_fail_message";  //appid_open_notice_fail_message

    public static final String APPID_OPEN_PUSH_SUCCESS_MESSAGE = "appid_open_push_success_message";  //appid_open_push_success_message
    public static final String APPID_OPEN_PUSH_FAIL_MESSAGE = "appid_open_push_fail_message";  //appid_open_push_success_message


    public static final String APPID_CLOSE_NOTICE_SUCCESS_MESSAGE = "appid_close_notice_success_message";  //appid_close_notice_success_message
    public static final String APPID_CLOSE_NOTICE_FAIL_MESSAGE = "appid_close_notice_fail_message";  //appid_close_notice_fail_message

    public static final String APPID_CLOSE_PUSH_SUCCESS_MESSAGE = "appid_close_push_success_message";  //appid_close_push_success_message
    public static final String APPID_CLOSE_PUSH_FAIL_MESSAGE = "appid_close_push_fail_message";  //appid_close_push_fail_message
    public static final String FUYOU_RATE_MAPPING = "fuyou_rate_mapping";  //富友费率映射阿波罗



    public static final String CONTENT = "content";
    public static final String TITLE = "title";
    public static final String BODY = "body";
    public static final String TYPE = "type";
    public static final String DEST = "dest";
    public static final String LOCATION = "location";

    public final static String MSG = "msg";
    public final static String AUDIO = "audio";
    public final static String AUTHOR = "author";













}
