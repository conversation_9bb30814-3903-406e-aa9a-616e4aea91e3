package com.wosai.upay.job.model;

import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/1
 */
@Data
public class MerchantFeature {

    /**
     * 商户号
     */
    private String merchantSn;

    /**
     * 商户名称
     */
    private String name;

    /**
     * 商户类型 1-小微 2-个体户 3-企业商户 4-组织商户
     */
    private String type;

    /**
     * 区唯一编码
     */
    private String districtCode;

    /**
     * 省唯一编码
     */
    private String provinceCode;

    /**
     * 市唯一编码
     */
    private String cityCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市名称
     */
    private String cityName;


    /**
     * 商户行业
     */
    private String industry;

    /**
     * 所属推广组织path(从一级开始)
     */
    private String promotionOrganizationPath;

    /**
     * 商户所属组织path(从一级开始)
     */
    private String organizationPath;

    /**
     * 银行账户类型 1-对私 2-对公
     */
    private String bankAccountType;

    /**
     * 法人类型 0-非法人 1-法人
     */
    private String legalPersonType;

    /**
     * 结算账户类型 1-法人对私 2-非法人对私 3-普通对公 4-其他对公 999-其他
     */
    private String settlementAccountType;

    /**
     * 个人证件类型 1-身份证 2-港澳居民来往内地通行证 3-台湾居民来往大陆通行证 4-非中华人民共和国护照 5-中国护照 6-港澳居民居住证 7-台湾居民居住证
     */
    private String personalCertificateType;

    /**
     * 商户开通的应用列表json
     */
    private String openedBusinessAppIdListJson;
    /**
     * 目标入网收单机构 lklV3 haike
     */
    private String acquirer;
    /**
     * 品牌商户支付模式
     * 2商家模式 3微信品牌模式 4支付宝品牌模式 5支付宝微信品牌模式
     */
    private String paymentMode;

    /**
     * 额外特征
     */
    private ExtraFeature extraFeature;

    @Data
    public class ExtraFeature {

        private MerchantBusinessLicenseInfo merchantBusinessLicenseInfo;

        private AccountInfo accountInfo;

        @Data
        public class AccountInfo {

            private Integer accountType;

            private String identityId;

            private String holderName;
        }
    }
}
