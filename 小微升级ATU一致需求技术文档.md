# 小微升级ATU一致需求技术文档

## 1. 概述

小微升级ATU一致需求是merchant-contract-job项目中的一个重要功能模块，主要用于处理商户从小微商户升级为个体工商户或企业商户时，确保支付宝(A)和微信(T)的实名认证状态与收钱吧(U)系统保持一致。该功能通过V3版本的营业执照认证任务来实现。

## 2. 核心流程架构

### 2.1 整体架构图

```
CRM申请单 → BusinessLicenceCertificationV3Task.insertTask()
    ↓
创建主任务和子任务
    ↓
AbstractInternalScheduleTaskHandleTemplate.processSingleMainTaskTemplate()
    ↓
BusinessLicenceCertificationV3Task.handleSingleSubTask()
    ↓
MicroUpgradeV3TaskHandler.handleMicroUpgrade()
    ↓
三种子任务类型处理：
1. 重新入网任务 (SUB_TASK_TYPE_RE_CONTRACT)
2. 实名认证任务 (SUB_TASK_TYPE_PAYWAY_AUTH)  
3. 参数切换任务 (SUB_TASK_TYPE_CHANGE_PARAMS)
```

### 2.2 任务依赖关系

```
账户验证任务(可选)
    ↓
主要进件任务 → 其他进件任务
    ↓
实名认证任务
    ↓
参数切换任务
```

## 3. 关键组件详解

### 3.1 入口点：BusinessLicenceCertificationV3Task.insertTask()

**功能**：接收CRM营业执照申请单，创建小微升级主任务

**核心逻辑**：
- 验证商户存在性
- 转换申请单为内部DTO
- 调用`doInsertTaskByLicenseAuditApply()`创建任务

<augment_code_snippet path="merchant-contract-job-war/src/main/java/com/wosai/upay/job/refactor/task/license/BusinessLicenceCertificationV3Task.java" mode="EXCERPT">
````java
public SubmitResultForCrmInfoManageDTO insertTask(CrmInformationManagementApplyFormDTO applyReq) {
    Optional<String> snOpt = merchantBasicInfoBiz.getMerchantSnByMerchantId(applyReq.getMerchantId());
    if (!snOpt.isPresent()) {
        return SubmitResultForCrmInfoManageDTO.fail("商户不存在");
    }
    String merchantSn = snOpt.get();
    BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO = convertToBusinessLicenseAuditApplyDTO(applyReq);
    doInsertTaskByLicenseAuditApply(merchantSn, applyReq.getFieldAppInfoId(), businessLicenseAuditApplyDTO);
    return SubmitResultForCrmInfoManageDTO.success();
}
````
</augment_code_snippet>

### 3.2 任务创建：doInsertTaskByLicenseAuditApply()

**功能**：构建主任务和所有子任务，设置任务依赖关系

**关键特性**：
- 强制设置`isMicroUpgrade = Boolean.TRUE`
- 根据是否需要更新银行账户决定是否创建账户验证任务
- 设置任务依赖链：账户验证 → 其他子任务

<augment_code_snippet path="merchant-contract-job-war/src/main/java/com/wosai/upay/job/refactor/task/license/BusinessLicenceCertificationV3Task.java" mode="EXCERPT">
````java
public void doInsertTaskByLicenseAuditApply(String merchantSn, Integer fieldAppInfoId, BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO) {
    boolean isMicroUpgrade = Boolean.TRUE;
    InternalScheduleMainTaskDO mainTask = buildMainTask(merchantSn, isMicroUpgrade, businessLicenseAuditApplyDTO, fieldAppInfoId);
    // 构建账户验证任务和其他子任务
    // 设置任务依赖关系
    //当前逻辑是：对于多通道商户必须等待当前正在使用的通道入网成功才可以开始调度,如果当前商户有AT，则需要等待AT授权完成，然后才能进行切换参数,所以主要流程是主通道->副通道->主通道实名授权->切换参数
}
````
</augment_code_snippet>

### 3.3 任务调度模板：AbstractInternalScheduleTaskHandleTemplate

**功能**：提供统一的任务调度框架

**核心方法**：`processSingleMainTaskTemplate()`

**处理流程**：
1. 检查任务是否可调度
2. 主任务前置处理
3. **循环处理子任务** (`loopHandleSubTask()`)
4. 计算主任务状态
5. 更新任务状态
6. 主任务后置处理

<augment_code_snippet path="merchant-contract-job-war/src/main/java/com/wosai/upay/job/refactor/task/AbstractInternalScheduleTaskHandleTemplate.java" mode="EXCERPT">
````java
private void processSingleMainTaskTemplate(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> sortedSubTasks) {
    mainTaskDO.setLastScheduledTime(new Timestamp(System.currentTimeMillis()));
    if (CollectionUtils.isEmpty(sortedSubTasks) || Objects.equals(mainTaskDO.getEnableScheduledStatus(), ScheduleStatusEnum.NOT_CAN.getValue()) || mainTaskDO.taskCanScheduledTimeNotArrive()) {
        updateMainTaskScheduleTime(mainTaskDO.getId());
        return;
    }
    if (mainTaskDO.taskCanNotHandled()) {
        return;
    }
    mainTaskPreProcessor(mainTaskDO, sortedSubTasks);
    loopHandleSubTask(mainTaskDO, sortedSubTasks);
    calculateMainTaskStatus(mainTaskDO, sortedSubTasks);
    updateMainTaskAndSubTask(mainTaskDO, sortedSubTasks);
    mainTaskPostProcessor(mainTaskDO, sortedSubTasks);
}
````
</augment_code_snippet>

### 3.4 子任务处理：BusinessLicenceCertificationV3Task.handleSingleSubTask()

**功能**：根据任务类型分发到不同的处理器

**处理逻辑**：
- 账户验证任务 → `bankAccountVerifyTaskHandler`
- 小微升级任务 → `microUpgradeV3TaskHandler`

<augment_code_snippet path="merchant-contract-job-war/src/main/java/com/wosai/upay/job/refactor/task/license/BusinessLicenceCertificationV3Task.java" mode="EXCERPT">
````java
@Override
protected InternalScheduleSubTaskProcessResultBO handleSingleSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
    try {
        if (isAccountVerifySubTask(subTaskDO)) {
            return bankAccountVerifyTaskHandler.handleAccountVerify(mainTaskDO, subTaskDO);
        }
        boolean microUpgradeTask = isMicroUpgradeTask(mainTaskDO);
        if (microUpgradeTask) {
            return microUpgradeV3TaskHandler.handleMicroUpgrade(mainTaskDO, subTaskDO);
        }
        return InternalScheduleSubTaskProcessResultBO.fail("当前主任务不是小微升级AT一致性任务");
    } catch (Exception e) {
        log.error("BusinessLicenceCertificationV3Task handleSingleSubTask error, merchantSn:{}", mainTaskDO.getMerchantSn(), e);
        mainTaskDO.setResult("系统异常");
        return InternalScheduleSubTaskProcessResultBO.fail(e.getMessage());
    }
}
````
</augment_code_snippet>

## 4. 核心处理器：MicroUpgradeV3TaskHandler.handleMicroUpgrade()

### 4.1 主要功能

这是小微升级V3版本的核心处理逻辑，负责处理三种不同类型的子任务：

1. **重新入网任务** (`SUB_TASK_TYPE_RE_CONTRACT`)
2. **实名认证任务** (`SUB_TASK_TYPE_PAYWAY_AUTH`)
3. **参数切换任务** (`SUB_TASK_TYPE_CHANGE_PARAMS`)

### 4.2 处理流程

<augment_code_snippet path="merchant-contract-job-war/src/main/java/com/wosai/upay/job/refactor/task/license/micro/MicroUpgradeV3TaskHandler.java" mode="EXCERPT">
````java
public InternalScheduleSubTaskProcessResultBO handleMicroUpgrade(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
    if (isReContractSubTask(subTaskDO)) {
        return handleReContractSubTask(mainTaskDO, subTaskDO);
    }
    //当前正在使用的收单机构对应的子商户实名授权状态
    if(isAuthSubTask(subTaskDO)) {
        return handleAuthSubTask(mainTaskDO, subTaskDO);
    }
    InternalScheduleSubTaskProcessResultBO resultBO = handleChangeParamsSubTask(mainTaskDO, subTaskDO);
    if (resultBO.processStatusIsSuccess()) {
        updateClearanceAndCacheAfterTaskFinished(mainTaskDO.getMerchantSn());
        processMicroUpgradeSuccess(mainTaskDO);
    }
    if (resultBO.processStatusIsFail()) {
        updateClearanceAndCacheAfterTaskFinished(mainTaskDO.getMerchantSn());
    }
    return resultBO;
}
````
</augment_code_snippet>

### 4.3 三种子任务类型详解

#### 4.3.1 重新入网任务 (handleReContractSubTask)

**功能**：向收单机构重新提交商户进件申请

**状态处理**：
- `WAIT_PROCESS`（状态0）：发起进件请求
- `WAIT_EXTERNAL_RESULT`（状态2）：查询进件结果

**关键逻辑**：
- 主收单机构优先处理，其他收单机构需等待主收单机构完成
- 调用收单机构进件接口
- 进件成功后自动提交支付宝和微信实名认证（仅个体商户）

#### 4.3.2 实名认证任务 (handleAuthSubTask)

**功能**：等待支付宝和微信实名认证完成

**处理逻辑**：
1. **状态为0（待处理）时**：
   - 检查是否所有重新入网任务都已完成（成功或失败）
   - 如果未完成，设置状态为2（等待外部结果），状态标记为`WAIT_ALL_RE_CONTRACT_FINISHED_MARK`

2. **状态为2（等待外部结果）时**：
   - 检查当前主收单机构的进件任务是否完成
   - 如果主收单机构进件失败，直接失败
   - 如果主收单机构进件成功，查询支付宝和微信的实名认证状态

**关键判断条件**：
- 主收单机构进件任务ID存在且已完成
- 支付宝和微信都已实名认证成功

**状态标记**：
- `WAIT_ALL_RE_CONTRACT_FINISHED_MARK`：等待重新进件子任务全部执行结束
- `WAIT_ALL_MAIN_CONTRACT_FINISHED_MARK`：等待当前在用收单机构进件成功
- `WAIT_ALL_MAIN_CONTRACT_AUTH_MARK`：等待当前在用收单机构实名认证成功

#### 4.3.3 参数切换任务 (handleChangeParamsSubTask)

**功能**：更新商户的交易参数配置

**前置条件**：
- 实名认证必须完成
- 执行强制提现操作
- 等待指定时间后开始参数切换

**核心操作**：
- 关闭商户交易状态
- 强制提现
- 更新交易参数
- 切换收单机构（如需要）

## 5. 实名认证任务详细流程

<augment_code_snippet path="merchant-contract-job-war/src/main/java/com/wosai/upay/job/refactor/task/license/micro/MicroUpgradeV3TaskHandler.java" mode="EXCERPT">
````java
private InternalScheduleSubTaskProcessResultBO handleAuthSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO)  {
    if (subTaskDO.isWaitProcess()) {
        if (!isReContractSubTasksAllFinished(mainTaskDO.getId())) {
            subTaskDO.setStatusMark(WAIT_ALL_RE_CONTRACT_FINISHED_MARK);
            return  InternalScheduleSubTaskProcessResultBO.waitExternalResult("等待重新进件子任务全部执行结束");
        }
    }
    //当前主收单机构任务
    BusinessLicenseCertificationV3MainTaskContext mainTaskContext =  JSONObject.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV3MainTaskContext.class);
    final Long mainContractTaskId = mainTaskContext.getMainContractTaskId();
    //当前主收单机构任务未完成
    if(Objects.isNull(mainContractTaskId) || !isContractTaskFinished(getContractTaskStatus(mainContractTaskId).get())) {
        subTaskDO.setStatusMark(WAIT_ALL_MAIN_CONTRACT_FINISHED_MARK);
        return InternalScheduleSubTaskProcessResultBO.waitExternalResult("等待当前在用收单机构进件成功");
    }
````
</augment_code_snippet>

## 6. 任务类型常量定义

<augment_code_snippet path="merchant-contract-job-war/src/main/java/com/wosai/upay/job/refactor/task/license/BusinessLicenceCertificationV3Task.java" mode="EXCERPT">
````java
public static final String SUB_TASK_TYPE_RE_CONTRACT = "重新入网";
public static final String SUB_TASK_TYPE_CHANGE_PARAMS = "参数变更";
public static final String SUB_TASK_TYPE_PAYWAY_AUTH = "支付源实名认证";
public static final String SUB_TASK_TYPE_UPDATE_LICENSE = "营业执照更新";
public static final String SUB_TASK_TYPE_ACCOUNT_VERIFY = "结算账户校验";
public static final String SUB_TASK_TYPE_ACCOUNT_CHANGE = "结算账户变更";
````
</augment_code_snippet>

## 7. 状态标记说明

系统使用多种状态标记来控制任务执行流程：

- `WAIT_ALL_RE_CONTRACT_FINISHED_MARK`：等待重新进件子任务全部执行结束
- `WAIT_ALL_MAIN_CONTRACT_FINISHED_MARK`：等待当前在用收单机构进件成功
- `WAIT_ALL_MAIN_CONTRACT_AUTH_MARK`：等待当前在用收单机构实名认证成功
- `WAIT_FEW_MINUTES_MARK`：等待几分钟
- `WAIT_CHANGE_ACQUIRER_FINISHED_MARK`：等待切收单机构任务结束

### 7.1 任务状态说明

- **状态0（WAIT_PROCESS）**：待处理，任务首次执行
- **状态2（WAIT_EXTERNAL_RESULT）**：等待外部结果，任务已提交等待外部系统响应
- **状态3（PROCESS_SUCCESS）**：处理成功
- **状态4（PROCESS_FAIL）**：处理失败

## 8. 关键业务规则

### 8.1 V3版本特性

- **ATU一致性**：确保支付宝(A)、微信(T)、收钱吧(U)三方实名认证状态一致
- **强制实名认证**：V3版本要求AT也要报备并且授权，只有在授权完成才可以进行切换参数
- **个体商户自动实名**：个体商户会自动提交支付宝和微信实名认证

### 8.2 任务依赖规则

1. **有账户验证时**：账户验证 → 主要进件 → 其他进件 → 实名授权 → 参数切换
2. **无账户验证时**：主要进件 → 其他进件 → 实名授权 → 参数切换
3. **实名授权和其他进件任务都依赖主要进件任务**

### 8.3 收单机构处理

- 优先处理当前使用的收单机构（确保其在第一位）
- 再处理其他间连收单机构
- 自动去重，避免重复处理

## 9. 异常处理机制

### 9.1 任务级异常处理

- 子任务异常会影响主任务状态
- 依赖任务失败会导致后续任务被禁用
- 系统异常会触发重试机制

### 8.2 业务级异常处理

- 进件失败会记录详细错误信息
- 实名认证超时会继续等待
- 参数切换失败会回滚相关状态

## 9. 监控和日志

系统在关键节点都有详细的日志记录：

- 任务创建和状态变更
- 进件请求和响应
- 实名认证状态查询
- 参数切换操作结果

## 10. 测试建议

### 10.1 功能测试要点

1. **正常流程测试**：验证完整的小微升级流程
2. **异常场景测试**：模拟进件失败、实名认证超时等场景
3. **依赖关系测试**：验证任务依赖关系是否正确
4. **状态一致性测试**：确保ATU三方状态一致

### 10.2 性能测试要点

1. **并发处理能力**：测试多个商户同时升级的处理能力
2. **任务调度效率**：验证任务调度的时效性
3. **资源消耗监控**：监控内存和数据库连接使用情况

## 11. 部署和配置

### 11.1 关键配置项

- `micro_upgrade_v3_rule`：V3版本开关配置
- `getMicroUpgradeWaitMinutes()`：参数切换等待时间
- 各收单机构的支持配置

### 11.2 数据库表

主要涉及的表：
- `internal_schedule_main_task`：主任务表
- `internal_schedule_sub_task`：子任务表
- `contract_status`：商户进件状态表
- `merchant_provider_params`：商户交易参数表

---

**注意**：本文档基于当前代码版本编写，如有代码变更，请及时更新文档内容。
