package com.wosai.upay.job.refactor.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.MerchantTypeEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.shouqianba.cua.utils.json.JSON;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.shouqianba.cua.utils.stream.ExtCollectors;
import com.shouqianba.cua.utils.thread.ThreadPoolWorker;
import com.wosai.assistant.response.UserBean;
import com.wosai.assistant.service.UserRpcService;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.enume.LegalPersonTypeEnum;
import com.wosai.upay.job.enume.MerchantSettlementAccountTypeEnum;
import com.wosai.upay.job.externalservice.brand.BrandBusinessClient;
import com.wosai.upay.job.externalservice.brand.model.BrandDetailInfoQueryResp;
import com.wosai.upay.job.externalservice.brand.model.BrandPaymentModeQueryResp;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.biz.rule.GroupCombinedStrategyBiz;
import com.wosai.upay.job.refactor.dao.McRuleGroupDAO;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.bo.ContractRuleDecisionEvaluateResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeaturePropertyBO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRuleDetailDO;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRulesDecisionDO;
import com.wosai.upay.job.refactor.model.entity.McRuleGroupDO;
import com.wosai.upay.job.refactor.model.enums.*;
import com.wosai.upay.job.refactor.service.McRulesDecisionService;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import com.wosai.upay.job.refactor.service.localcache.McRulesLocalCacheService;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiFunction;
import java.util.stream.Collectors;


/**
 * 进件规则决策表表Service层 {@link GroupRouteRulesDecisionDO}
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class McRulesDecisionServiceImpl implements McRulesDecisionService {


    private final AcquirerFacade acquirerFacade;

    private final McRulesLocalCacheService mcRulesLocalCacheService;

    private final MerchantService merchantService;

    private final IMerchantService iMerchantService;

    private final OrganizationService organizationService;

    private final MerchantBusinessLicenseService mcMerchantBusinessLicenseService;

    private final MerchantBankService merchantBankService;

    private final GroupCombinedStrategyBiz groupCombinedStrategyBiz;

    private final DistrictsServiceV2 districtsServiceV2;

    private final UserRpcService userRpcService;

    private final ApplicationApolloConfig applicationApolloConfig;

    private final McRuleGroupDAO mcRuleGroupDAO;

    private final CommonAppInfoService commonAppInfoService;

    private final BrandBusinessClient brandBusinessClient;

    private final ContractStatusMapper contractStatusMapper;

    @Autowired
    public McRulesDecisionServiceImpl(
            AcquirerFacade acquirerFacade,
            McRulesLocalCacheService mcRulesLocalCacheService,
            MerchantService merchantService,
            IMerchantService iMerchantService,
            OrganizationService organizationService,
            MerchantBusinessLicenseService mcMerchantBusinessLicenseService,
            MerchantBankService merchantBankService,
            GroupCombinedStrategyBiz groupCombinedStrategyBiz,
            DistrictsServiceV2 districtsServiceV2,
            ApplicationApolloConfig applicationApolloConfig,
            McRuleGroupDAO mcRuleGroupDAO,
            UserRpcService userRpcService,
            CommonAppInfoService commonAppInfoService,
            BrandBusinessClient brandBusinessClient,
            ContractStatusMapper contractStatusMapper) {
        this.acquirerFacade = acquirerFacade;
        this.mcRulesLocalCacheService = mcRulesLocalCacheService;
        this.merchantService = merchantService;
        this.iMerchantService = iMerchantService;
        this.organizationService = organizationService;
        this.mcMerchantBusinessLicenseService = mcMerchantBusinessLicenseService;
        this.merchantBankService = merchantBankService;
        this.groupCombinedStrategyBiz = groupCombinedStrategyBiz;
        this.districtsServiceV2 = districtsServiceV2;
        this.userRpcService = userRpcService;
        this.commonAppInfoService = commonAppInfoService;
        this.applicationApolloConfig = applicationApolloConfig;
        this.mcRuleGroupDAO = mcRuleGroupDAO;
        this.brandBusinessClient = brandBusinessClient;
        this.contractStatusMapper = contractStatusMapper;
        initLogicalOperationFunctionMap();
    }

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    private final EnumMap<LogicalOperationTypeEnum, BiFunction<MerchantFeaturePropertyBO, String, Boolean>> logicalOperationFunctionMap = Maps.newEnumMap(LogicalOperationTypeEnum.class);

    private void initLogicalOperationFunctionMap() {
        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.EQUAL, (merchantFeaturePropertyBO, ruleRequiredPropertyValue)
                -> StringUtils.equals(getMerchantFeatureProperty(merchantFeaturePropertyBO),
                getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue)));

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.NOT_EQUAL, (merchantFeaturePropertyBO, ruleRequiredPropertyValue)
                -> !StringUtils.equals(getMerchantFeatureProperty(merchantFeaturePropertyBO),
                getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue)));

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.CONTAIN, (merchantFeaturePropertyBO, ruleRequiredPropertyValue) -> {
            if (StringUtils.isBlank(getMerchantFeatureProperty(merchantFeaturePropertyBO))) {
                return false;
            }
            return StringUtils.contains(getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue),
                    getMerchantFeatureProperty(merchantFeaturePropertyBO));
        });

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.NOT_CONTAIN, (merchantFeaturePropertyBO, ruleRequiredPropertyValue) -> {
            if (StringUtils.isBlank(getMerchantFeatureProperty(merchantFeaturePropertyBO))) {
                return true;
            }
            return !StringUtils.contains(getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue),
                    getMerchantFeatureProperty(merchantFeaturePropertyBO));
        });

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.BE_CONTAINED, (merchantFeaturePropertyBO, ruleRequiredPropertyValue) -> {
            if (StringUtils.isBlank(getMerchantFeatureProperty(merchantFeaturePropertyBO))) {
                return false;
            }
            return StringUtils.contains(getMerchantFeatureProperty(merchantFeaturePropertyBO),
                    getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue));
        });

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.NOT_BE_CONTAINED, (merchantFeaturePropertyBO, ruleRequiredPropertyValue) -> {
            if (StringUtils.isBlank(getMerchantFeatureProperty(merchantFeaturePropertyBO))) {
                return true;
            }
            return !StringUtils.contains(getMerchantFeatureProperty(merchantFeaturePropertyBO),
                    getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue));
        });

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.IN, (merchantFeaturePropertyBO, ruleRequiredPropertyValue)
                -> parseInputToList(ruleRequiredPropertyValue).contains(getMerchantFeatureProperty(merchantFeaturePropertyBO)));

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.NOT_IN, (merchantFeaturePropertyBO, ruleRequiredPropertyValue)
                -> !parseInputToList(ruleRequiredPropertyValue).contains(getMerchantFeatureProperty(merchantFeaturePropertyBO)));

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.START_WITH, (merchantFeaturePropertyBO, ruleRequiredPropertyValue)
                -> StringUtils.startsWith(getMerchantFeatureProperty(merchantFeaturePropertyBO),
                getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue)));

        logicalOperationFunctionMap.put(LogicalOperationTypeEnum.NOT_START_WITH, (merchantFeaturePropertyBO, ruleRequiredPropertyValue)
                -> !StringUtils.startsWith(getMerchantFeatureProperty(merchantFeaturePropertyBO),
                getRealRuleRequiredPropertyValue(ruleRequiredPropertyValue)));
    }

    private @Nullable String getRealRuleRequiredPropertyValue(String ruleRequiredPropertyValue) {
        return parseInputToList(ruleRequiredPropertyValue).stream().filter(Objects::nonNull).findFirst().orElse(null);
    }

    private List<String> parseInputToList(String input) {
        if (StringUtils.isBlank(input)) {
            return Collections.emptyList();
        }
        if (isJsonStringArray(input)) {
            return JSON.parseArray(input, String.class);
        }
        return Lists.newArrayList(input);
    }

    private boolean isJsonStringArray(String input) {
        return input.startsWith("[") && input.endsWith("]");
    }


    /**
     * 根据商户号和所属推广组织获取进件通道组
     *
     * @param merchantSn     商户号
     * @param organizationId 组织id
     * @return 进件报备规则组策略组合detail列表
     */
    @Override
    @SuppressWarnings("java:S3516")
    public Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>> chooseGroupBySnAndOrg(String merchantSn, String organizationId){
        if (StringUtils.isBlank(merchantSn)) {
            return new Tuple2<>(Collections.emptySet(), Collections.emptySet());
        }
        return chooseGroupByMerchantFeature(getMerchantFeature(merchantSn, organizationId));
    }


    private MerchantFeatureBO getMerchantFeature(String merchantSn, String organizationId) {
        MerchantInfo merchantBasicInfo = getMerchantBasicInfo(merchantSn);
        if (Objects.isNull(merchantBasicInfo)) {
            throw new CommonPubBizException("商户不存在, merchantSn = {}" + merchantSn);
        }
        HashMap<String, String> distinctRequestMap = Maps.newHashMap();
        AtomicReference<String> promotionOrganizationPathReference = new AtomicReference<>();
        AtomicReference<String> organizationPathReference = new AtomicReference<>();
        AtomicReference<MerchantBusinessLicenseInfo> licenseInfoAtomicReference = new AtomicReference<>();
        AtomicReference<Map<String, ?>> accountInfoReference = new AtomicReference<>();
        AtomicReference<District> districtAtomicReference = new AtomicReference<>();
        ThreadPoolWorker<Object> poolWorker = ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance());
        poolWorker
                .addWork(() ->  promotionOrganizationPathReference.set(getPromotionOrganizationPath(merchantBasicInfo, organizationId)))
                .addWork(() -> organizationPathReference.set(getMerchantOrganizationPath(merchantBasicInfo)))
                .addWork(() -> licenseInfoAtomicReference.set(mcMerchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantBasicInfo.getId(), devCode)))
                .addWork(() -> accountInfoReference.set(getBankAccount(merchantBasicInfo.getId(), merchantSn)));
        if (StringUtils.isNotBlank(merchantBasicInfo.getDistrict_code())) {
            distinctRequestMap.put("code", merchantBasicInfo.getDistrict_code());
            poolWorker.addWork(() -> districtAtomicReference.set(districtsServiceV2.getDistrict(distinctRequestMap)));
        }
        poolWorker.doWorks();
        return getMerchantFeature(merchantBasicInfo, licenseInfoAtomicReference, organizationPathReference,
                promotionOrganizationPathReference, accountInfoReference, districtAtomicReference);
    }

    private String getMerchantOrganizationPath(MerchantInfo merchantBasicInfo) {
        String organizationId = BeanUtil.getPropString(iMerchantService.getMerchantBySn(merchantBasicInfo.getSn()), "organization_id");
        if (StringUtils.isBlank(organizationId)) {
            return StringUtils.EMPTY;
        }
        return MapUtils.getString(organizationService.getOrganization(organizationId), "path");
    }

    private String getPromotionOrganizationPath(MerchantInfo merchantBasicInfo, String organizationId) {
        if (StringUtils.isNotBlank(organizationId)) {
            return MapUtils.getString(organizationService.getOrganization(organizationId), "path");
        }
        Map<String, UserBean> userMap = userRpcService.getUserByMerchantIds(Lists.newArrayList(merchantBasicInfo.getId()));
        if (MapUtils.isEmpty(userMap) || Objects.isNull(userMap.get(merchantBasicInfo.getId()))) {
            return StringUtils.EMPTY;
        }
        String organizationCodes = userMap.get(merchantBasicInfo.getId()).getOrganizationCodes();
        if (StringUtils.isBlank(organizationCodes)) {
            return StringUtils.EMPTY;
        }
        return organizationCodes.replaceAll("/", ",");
    }



    private MerchantFeatureBO getMerchantFeature(MerchantInfo merchantBasicInfo,
                                                 AtomicReference<MerchantBusinessLicenseInfo> licenseInfoAtomicReference,
                                                 AtomicReference<String> organizationPathReference,
                                                 AtomicReference<String> promotionOrganizationReference,
                                                 AtomicReference<Map<String, ?>> accountInfoReference,
                                                 AtomicReference<District> districtAtomicReference) {
        MerchantFeatureBO merchantFeatureBO = new MerchantFeatureBO();
        merchantFeatureBO.setMerchantSn(merchantBasicInfo.getSn());
        merchantFeatureBO.setName(merchantBasicInfo.getName());
        merchantFeatureBO.setDistrictCode(merchantBasicInfo.getDistrict_code());
        District district = districtAtomicReference.get();
        if (Objects.nonNull(district)) {
            merchantFeatureBO.setProvinceCode(district.getProvince_code());
            merchantFeatureBO.setCityCode(district.getCity_code());
        }
        merchantFeatureBO.setProvinceName(merchantBasicInfo.getProvince());
        merchantFeatureBO.setCityName(merchantBasicInfo.getCity());
        merchantFeatureBO.setIndustry(merchantBasicInfo.getIndustry());
        merchantFeatureBO.setType(getMerchantType(licenseInfoAtomicReference.get()));
        // 推广组织使用path前缀匹配
        merchantFeatureBO.setPromotionOrganizationPath(promotionOrganizationReference.get());
        merchantFeatureBO.setOrganizationPath(organizationPathReference.get());
        Map<String, ?> accountInfoMap = accountInfoReference.get();
        MerchantFeatureBO.ExtraFeature extraFeature = merchantFeatureBO.new ExtraFeature();
        MerchantFeatureBO.ExtraFeature.AccountInfo accountInfo = extraFeature.new AccountInfo();
        accountInfo.setAccountType(MapUtils.isEmpty(accountInfoMap) ? null : MapUtils.getInteger(accountInfoMap, MerchantBankAccount.TYPE));
        accountInfo.setIdentityId(MapUtils.isEmpty(accountInfoMap) ? null : BeanUtil.getPropString(accountInfoMap, MerchantBankAccount.IDENTITY));
        accountInfo.setHolderName(MapUtils.isEmpty(accountInfoMap) ? null : BeanUtil.getPropString(accountInfoMap, MerchantBankAccount.HOLDER));
        extraFeature.setAccountInfo(accountInfo);
        extraFeature.setMerchantBusinessLicenseInfo(licenseInfoAtomicReference.get());
        merchantFeatureBO.setExtraFeature(extraFeature);
        EnumUtils.ofNullable(BankAccountTypeEnum.class, accountInfo.getAccountType())
                .ifPresent(t -> merchantFeatureBO.setBankAccountType(t.getValue().toString()));
        populateLegalPersonAndSettlementAccountType(merchantFeatureBO);
        if (Objects.nonNull(licenseInfoAtomicReference.get()) && !Objects.equals(licenseInfoAtomicReference.get().getType(), BusinessLicenseTypeEnum.MICRO.getValue())) {
            merchantFeatureBO.setPersonalCertificateType(licenseInfoAtomicReference.get().getLegal_person_id_type().toString());
        } else {
            merchantFeatureBO.setPersonalCertificateType(MapUtils.isEmpty(accountInfoMap) ? null : MapUtils.getString(accountInfoMap, MerchantBankAccount.ID_TYPE));
        }
        merchantFeatureBO.setOpenedBusinessAppIdListJson(getOpenedBusinessAppIdListJson(merchantBasicInfo.getId()));

        populateBrandInfo(merchantFeatureBO);

        return merchantFeatureBO;
    }

    private void populateBrandInfo(MerchantFeatureBO merchantFeatureBO) {
        BrandPaymentModeQueryResp brandMerchantInfo = brandBusinessClient.getBrandPaymentModeForContract(merchantFeatureBO.getExtraFeature().getMerchantBusinessLicenseInfo().getMerchant_id());
        if (Objects.nonNull(brandMerchantInfo)) {
            BrandDetailInfoQueryResp brandDetailInfo = brandBusinessClient.getBrandDetailInfoByBrandId(brandMerchantInfo.getBrandId());
            // 如果入网商户和品牌主商户是同一个，则不需要设置品牌信息，要走商户模式入网流程
            if (Objects.equals(merchantFeatureBO.getMerchantSn(), brandDetailInfo.getMainMerchantSn())) {
                return;
            }
            String acquirer = contractStatusMapper.selectByMerchantSn(brandDetailInfo.getMainMerchantSn()).getAcquirer();
            merchantFeatureBO.setAcquirer(acquirer);
            merchantFeatureBO.setPaymentMode(brandMerchantInfo.getPaymentMode().getCode().toString());
        }
    }

    private String getOpenedBusinessAppIdListJson(String merchantId) {
        HashMap<Object, Object> requestMap = Maps.newHashMap();
        requestMap.put("merchant_id", merchantId);
        try {
            ListResult appInfos = commonAppInfoService.findAppInfos(new PageInfo(1, 10000), requestMap);
            if (Objects.isNull(appInfos)) {
                return StringUtils.EMPTY;
            }
            List<Map> records = appInfos.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                return StringUtils.EMPTY;
            }
            return JSON.toJSONString(records.stream().map(t -> MapUtils.getString(t, "app_id")).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("获取商户开通的业务应用失败, merchantId:{}", merchantId, e);
            return StringUtils.EMPTY;
        }
    }

    private void populateLegalPersonAndSettlementAccountType(MerchantFeatureBO merchantFeatureBO) {
        MerchantFeatureBO.ExtraFeature extraFeature = merchantFeatureBO.getExtraFeature();
        MerchantBusinessLicenseInfo licenseInfo = extraFeature.getMerchantBusinessLicenseInfo();
        MerchantFeatureBO.ExtraFeature.AccountInfo accountInfo = extraFeature.getAccountInfo();
        MerchantSettlementAccountTypeEnum settlementAccountType = MerchantSettlementAccountTypeEnum.OTHER;
        Integer accountType = accountInfo.getAccountType();
        if (Objects.isNull(licenseInfo) || Objects.isNull(accountType)) {
            log.warn("商户营业执照或者账户类型为空,商户号: {}", merchantFeatureBO.getMerchantSn());
            return;
        }
        boolean legalPersonAccount = StringUtils.isNotBlank(accountInfo.getIdentityId())
                && StringUtils.isNotBlank(licenseInfo.getLegal_person_id_number())
                && StringUtils.equals(accountInfo.getIdentityId(), licenseInfo.getLegal_person_id_number());
        merchantFeatureBO.setLegalPersonType(legalPersonAccount ? LegalPersonTypeEnum.LEGAL_PERSON.getValue().toString()
                : LegalPersonTypeEnum.NOT_LEGAL_PERSON.getValue().toString());
        if (BankAccountTypeEnum.isPersonal(accountType)) {
            settlementAccountType = legalPersonAccount ? MerchantSettlementAccountTypeEnum.LEGAL_PRIVATE
                    : MerchantSettlementAccountTypeEnum.NON_LEGAL_PRIVATE;
        } else if (BankAccountTypeEnum.isPublic(accountType)){
            settlementAccountType = StringUtils.equals(licenseInfo.getName(), accountInfo.getHolderName()) ? MerchantSettlementAccountTypeEnum.COMMON_PUBLIC
                    : MerchantSettlementAccountTypeEnum.OTHER_PUBLIC;
        }
        merchantFeatureBO.setSettlementAccountType(settlementAccountType.getValue().toString());
    }


    private Map<String, ?> getBankAccount(String merchantId, String merchantSn) {
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", merchantId, "default_status", ValidStatusEnum.VALID.getValue())
        );
        if (CollectionUtils.isEmpty(listResult.getRecords())) {
            throw new CommonPubBizException("获取商户卡信息为空merchantSn:" + merchantSn + "merchantId:" + merchantId);
        }
        return listResult.getRecords().get(0);
    }


    private String getMerchantType(MerchantBusinessLicenseInfo licenseInfo) {
        if (Objects.isNull(licenseInfo)) {
            return StringUtils.EMPTY;
        }
        Integer type = licenseInfo.getType();
        if (BusinessLicenseTypeEnum.isMicro(type)) {
            return MerchantTypeEnum.SMALL_MICRO_MERCHANT.getValue().toString();
        }
        if (BusinessLicenseTypeEnum.isIndividual(type)) {
            return MerchantTypeEnum.INDIVIDUAL_MERCHANT.getValue().toString();
        }
        if (BusinessLicenseTypeEnum.isEnterprise(type)) {
            return MerchantTypeEnum.COMPANY_MERCHANT.getValue().toString();
        }
        if (BusinessLicenseTypeEnum.isOrganization(type)) {
            return MerchantTypeEnum.ORGANIZATION_MERCHANT.getValue().toString();
        }
        return StringUtils.EMPTY;
    }


    private MerchantInfo getMerchantBasicInfo(String merchantSn) {
        return merchantService.getMerchantBySn(merchantSn, devCode);
    }


    /**
     * 根据商户特征获取进件通道组
     *
     * @param merchantFeature 商户特征
     * @return 进件报备规则组策略组合detail列表
     */
    @Override
    public Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>> chooseGroupByMerchantFeature(MerchantFeatureBO merchantFeature){
        if (Objects.isNull(merchantFeature)) {
            return new Tuple2<>(Collections.emptySet(), Collections.emptySet());
        }
        List<GroupRouteRulesDecisionDO> mcRulesDecisionList = listAllValidMcRulesDecisionDOS();
        List<GroupRouteRulesDecisionDO> topLevelRulesDecisions = mcRulesDecisionList
                .stream()
                .filter(t -> Objects.equals(t.getParentId(), 0L))
                .sorted(Comparator.comparingInt(GroupRouteRulesDecisionDO::getPriority))
                .collect(Collectors.toList());
        Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>> result = loopRuleDecisionsReturnGroupDetails(merchantFeature, topLevelRulesDecisions);
        log.info("根据商户特征和进件规则匹配收单机构,merchantSn:{}, 商户特征:{}, 匹配结果: {}", merchantFeature.getMerchantSn(), merchantFeature, JSON.toJSONString(result));
        return result;
    }

    private Tuple2<Set<GroupCombinedStrategyDetailDO>, Set<String>>  loopRuleDecisionsReturnGroupDetails(MerchantFeatureBO merchantFeature, List<GroupRouteRulesDecisionDO> topLevelRulesDecisions) {
        Set<String> forbiddenGroupIdSets = new HashSet<>();
        Set<String> forbiddenMessageSets = new HashSet<>();
        for (GroupRouteRulesDecisionDO decision : topLevelRulesDecisions) {
            log.debug("rule decision, priority={}, name={}, id={}, forbiddenGroupIdSets={}", decision.getPriority(), decision.getName(), decision.getId(), forbiddenGroupIdSets);
            ContractRuleDecisionEvaluateResultBO evaluateResult = evaluateRuleDecisionNodeWithCatchException(decision, merchantFeature);
            if (evaluateResult.notPass()) {
                continue;
            }
            Set<GroupCombinedStrategyDetailDO> validStrategyDetails = listValidAndSortedGroupCombinesDetails(decision, merchantFeature, forbiddenGroupIdSets, evaluateResult, forbiddenMessageSets);
            if (isChooseType(decision, ChooseTypeEnum.ONLY_CAN) || (isChooseType(decision, ChooseTypeEnum.ENABLE) && !validStrategyDetails.isEmpty())) {
                log.info("商户命中规则: sn={}, rule_name={}, 规则决策明细: {}, 商户特征: {}", merchantFeature.getMerchantSn(), decision.getName(), evaluateResult, merchantFeature);
                return new Tuple2<>(validStrategyDetails, forbiddenMessageSets);
            }
        }
        log.warn("商户进件规则匹配未命中任何收单机构, merchantSn:{}, forbiddenGroupIdSets:{}", merchantFeature.getMerchantSn(), forbiddenGroupIdSets);
        Set<GroupCombinedStrategyDetailDO> groupCombinedStrategyDetailDOS = defaultWhenNoMatchedGroup(forbiddenGroupIdSets, merchantFeature, forbiddenMessageSets).<Set<GroupCombinedStrategyDetailDO>>map(Sets::newHashSet).orElseGet(Sets::newHashSet);
        return new Tuple2<>(groupCombinedStrategyDetailDOS, forbiddenMessageSets);
    }

    public Optional<GroupCombinedStrategyDetailDO> defaultWhenNoMatchedGroup(Set<String> forbiddenGroupIdSets, MerchantFeatureBO merchantFeature, Set<String> evaluateResultBOs) {
        GroupCombinedStrategyDetailDO groupCombinedStrategyDetailDO = new GroupCombinedStrategyDetailDO();
        List<String> inUseDefaultMcRuleGroup = applicationApolloConfig.listInUseDefaultMcRuleGroup();
        Map<String, String> acquirerToDefaultRuleGroupIdMap = mcRuleGroupDAO.listAll().stream()
                .filter(t -> inUseDefaultMcRuleGroup.contains(t.getGroupId())
                        && !forbiddenGroupIdSets.contains(t.getGroupId())
                        && Objects.equals(t.getStatus(), ValidStatusEnum.VALID.getValue())
                        && Objects.equals(t.getDefaultStatus(), DefaultStatusEnum.DEFAULT.getValue())
                        && acquirerFacade.getStrategy(t.getAcquirer()).map(strategy -> {
                    ContractGroupRuleVerifyResultBO contractGroupRuleVerifyResultBO = strategy.checkSatisfactionToAcquirerTemplate(merchantFeature);
                    if (!contractGroupRuleVerifyResultBO.isCheckPass()) {
                        evaluateResultBOs.add(contractGroupRuleVerifyResultBO.getMessage());
                    }
                    return  contractGroupRuleVerifyResultBO.isCheckPass();
                }).orElse(true))
                .collect(ExtCollectors.toMap(McRuleGroupDO::getAcquirer, McRuleGroupDO::getGroupId, (existing, replacement) -> existing));
        if (MapUtils.isEmpty(acquirerToDefaultRuleGroupIdMap)) {
            log.warn("defaultWhenNoMatchedGroup, 收单机构选取为空, merchantSn:{}", merchantFeature.getMerchantSn());
            return Optional.empty();
        }
        String groupId = acquirerToDefaultRuleGroupIdMap.entrySet().iterator().next().getValue();
        groupCombinedStrategyDetailDO.setGroupId(groupId);
        return Optional.of(groupCombinedStrategyDetailDO);
    }

    private ContractRuleDecisionEvaluateResultBO evaluateRuleDecisionNodeWithCatchException(GroupRouteRulesDecisionDO decision, MerchantFeatureBO merchantFeature) {
        try {
            return evaluateRuleDecisionNode(merchantFeature, decision);
        } catch (Exception e) {
            log.error("规则决策校验异常,商户:{},规则决策:{},决策id:{}", merchantFeature.getMerchantSn(), decision.getName(), decision.getId(), e);
            return ContractRuleDecisionEvaluateResultBO.fail();
        }
    }


    private Set<GroupCombinedStrategyDetailDO> listValidAndSortedGroupCombinesDetails(GroupRouteRulesDecisionDO decision, MerchantFeatureBO merchantFeature,
                                                                                      Set<String> forbiddenGroupIdSets, ContractRuleDecisionEvaluateResultBO evaluateResult, Set<String> forbiddenMessageSets) {
        List<GroupCombinedStrategyDetailDO> groupDetails = groupCombinedStrategyBiz.listGroupDetail(decision.getGroupStrategyId());
        String chooseType = decision.getChooseType();
        if (StringUtils.equals(chooseType, ChooseTypeEnum.UNABLE.getValue())) {
            log.info("商户命中UNABLE规则:{},规则决策明细:{},merchantSn:{}", decision.getName(), evaluateResult, merchantFeature.getMerchantSn());
            forbiddenMessageSets.add(evaluateResult.getMessage());
            forbiddenGroupIdSets.addAll(groupDetails.stream().map(GroupCombinedStrategyDetailDO::getGroupId).collect(Collectors.toSet()));
            return Collections.emptySet();
        }
        return groupDetails.parallelStream()
                .filter(detail -> !forbiddenGroupIdSets.contains(detail.getGroupId()))
                .filter(detail -> acquirerFacade.getStrategy(detail.getAcquirer())
                        .map(strategy -> {
                            ContractGroupRuleVerifyResultBO contractGroupRuleVerifyResultBO = strategy.checkSatisfactionToAcquirerTemplate(merchantFeature);
                            if (!contractGroupRuleVerifyResultBO.isCheckPass()) {
                                forbiddenMessageSets.add(contractGroupRuleVerifyResultBO.getMessage());
                            }
                            return  contractGroupRuleVerifyResultBO.isCheckPass();
                        }).orElse(true))
                .sorted(GroupCombinedStrategyDetailDO::compareTo)
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(GroupCombinedStrategyDetailDO::getGroupId, detail -> detail, (existing, replacement) -> existing, LinkedHashMap::new),
                        map -> new LinkedHashSet<>(map.values())));
    }


    private boolean isChooseType(GroupRouteRulesDecisionDO decision, ChooseTypeEnum type) {
        return StringUtils.equals(decision.getChooseType(), type.getValue());
    }


    private Map<Long, List<GroupRouteRuleDetailDO>> getValidRuleDetailMap() {
        return mcRulesLocalCacheService.listAllValidRuleDetails().stream()
                .collect(Collectors.groupingBy(GroupRouteRuleDetailDO::getRuleDecisionId));
    }

    private List<GroupRouteRulesDecisionDO> listAllValidMcRulesDecisionDOS() {
        return mcRulesLocalCacheService.listAllValidRuleDecisions();
    }


    /**
     * 计算规则决策节点
     * 如果节点且没有叶子节点,计算节点对应的rule detail
     * 如果节点有子节点,需要递归处理子节点的rule detail,节点根据子节点的判断返回
     * 节点如果有子节点,则节点没有对应的detail
     *
     * @param merchantFeature         商户特征
     * @param decisionNode            规则决策节点
     * @return 规则决策结果
     */
    private ContractRuleDecisionEvaluateResultBO evaluateRuleDecisionNode(MerchantFeatureBO merchantFeature, GroupRouteRulesDecisionDO decisionNode) {
        Map<Long/*ruleDecisionId*/, List<GroupRouteRuleDetailDO>> mcRuleDetailMap = getValidRuleDetailMap();
        List<GroupRouteRulesDecisionDO> mcRulesDecisionList = listAllValidMcRulesDecisionDOS();
        Map<Long/*parentId*/, List<GroupRouteRulesDecisionDO>> decisionParentIdDOSMap = mcRulesDecisionList.stream().collect(Collectors.groupingBy(GroupRouteRulesDecisionDO::getParentId));
        boolean existChildrenNode = decisionParentIdDOSMap.containsKey(decisionNode.getId());
        if (!existChildrenNode) {
            return evaluateLeafRuleDecisionNode(merchantFeature,decisionNode, mcRuleDetailMap.get(decisionNode.getId()), decisionNode.getRuleDetailConnectionType());
        }
        List<GroupRouteRulesDecisionDO> childDecisions = decisionParentIdDOSMap.get(decisionNode.getId());
        boolean andConnection = Objects.equals(decisionNode.getRuleConnectionType(), RuleConnectionTypeEnum.AND.getValue());
        ContractRuleDecisionEvaluateResultBO evaluateResult = null;
        for (GroupRouteRulesDecisionDO childDecision : childDecisions) {
            ContractRuleDecisionEvaluateResultBO decisionEvaluateResultBO = evaluateRuleDecisionNode(merchantFeature, childDecision);
            if (andConnection && !decisionEvaluateResultBO.isPass()) {
                evaluateResult = ContractRuleDecisionEvaluateResultBO.fail();
                break;
            }
            if (!andConnection && decisionEvaluateResultBO.isPass()) {
                evaluateResult = ContractRuleDecisionEvaluateResultBO.success(decisionEvaluateResultBO.getMessage());
                break;
            }
        }
        // 对于or命中,会记录子节点名称，对于and，直接记录父节点名称
        if (Objects.isNull(evaluateResult)) {
            evaluateResult = andConnection ? ContractRuleDecisionEvaluateResultBO.success("命中具体规则: " + decisionNode.getId() + " " +  decisionNode.getName())
                    : ContractRuleDecisionEvaluateResultBO.fail();
        }
        return evaluateResult;
    }


    /**
     * 计算规则决策叶子节点(根据对应的规则detail)
     * 如果对应的规则detail为空(例如兜底的`lklorg`),直接返回true
     * 如果连接类型ruleDetailConnectionType为空,默认取AND
     *
     * @param merchantFeature          商户特征
     * @param matchedRuleDetailDOS     规则detail列表
     * @param ruleDetailConnectionType 规则detail连接类型 and连接且没有为false的，结果为true. or连接且没有为true的，结果为false
     * @return true:规则决策通过 false:规则决策不通过
     */
    private ContractRuleDecisionEvaluateResultBO evaluateLeafRuleDecisionNode(MerchantFeatureBO merchantFeature,
                                                                              GroupRouteRulesDecisionDO decisionNode,
                                                                              List<GroupRouteRuleDetailDO> matchedRuleDetailDOS,
                                                                              String ruleDetailConnectionType) {
        if (CollectionUtils.isEmpty(matchedRuleDetailDOS)) {
            return ContractRuleDecisionEvaluateResultBO.success("规则为空");
        }
        boolean andConnection = Objects.equals(EnumUtils.ofNullable(RuleDetailConnectionTypeEnum.class, ruleDetailConnectionType).orElse(RuleDetailConnectionTypeEnum.AND).getValue(),
                RuleDetailConnectionTypeEnum.AND.getValue());
        ContractRuleDecisionEvaluateResultBO decisionEvaluateResultBO = null;
        for (GroupRouteRuleDetailDO matchedRuleDetailDO : matchedRuleDetailDOS) {
            boolean singleRuleEvaluateResult = doEvaluateSingleRuleDetail(merchantFeature, matchedRuleDetailDO);
            if (andConnection && !singleRuleEvaluateResult) {
                decisionEvaluateResultBO = ContractRuleDecisionEvaluateResultBO.fail();
                break;
            } else if (!andConnection && singleRuleEvaluateResult) {
                String objectPropertyValue = matchedRuleDetailDO.getObjectPropertyValue();
                if (StringUtils.isNotBlank(objectPropertyValue) && objectPropertyValue.length() > 100) {
                    objectPropertyValue = StringUtils.substring(matchedRuleDetailDO.getObjectPropertyValue(), 0, 100) + "...";
                }
                String detail = matchedRuleDetailDO.getObjectPropertyType() + "->" + matchedRuleDetailDO.getLogicalOperationType() + "->" + objectPropertyValue;
                decisionEvaluateResultBO = ContractRuleDecisionEvaluateResultBO.success("命中具体规则: " + decisionNode.getId() + " " + decisionNode.getName() + " ,detail: " + detail);
                break;
            }
        }
        if (Objects.isNull(decisionEvaluateResultBO)) {
            decisionEvaluateResultBO = andConnection ? ContractRuleDecisionEvaluateResultBO.success("命中具体规则: " + decisionNode.getId() + " " + decisionNode.getName())
                    : ContractRuleDecisionEvaluateResultBO.fail();
        }
        return decisionEvaluateResultBO;
    }


    /**
     * 根据商户特征,计算每个规则detail是否合规
     *
     * @param merchantFeature         商户特征
     * @param matchedRuleDetailDO     规则detail
     * @return true:符合
     */
    private boolean doEvaluateSingleRuleDetail(MerchantFeatureBO merchantFeature, GroupRouteRuleDetailDO matchedRuleDetailDO) {
        AtomicBoolean checkResult = new AtomicBoolean(false);
        String objectPropertyType = matchedRuleDetailDO.getObjectPropertyType();
        String logicalOperationType = matchedRuleDetailDO.getLogicalOperationType();
        String ruleRequiredPropertyValue = matchedRuleDetailDO.getObjectPropertyValue();
        String validatedRuleRequiredPropertyValue = StringUtils.isBlank(ruleRequiredPropertyValue) ? StringUtils.EMPTY : ruleRequiredPropertyValue;
        MerchantFeaturePropertyBO merchantFeaturePropertyBO = new MerchantFeaturePropertyBO(merchantFeature, objectPropertyType);
        EnumUtils.ofNullable(LogicalOperationTypeEnum.class, logicalOperationType)
                .ifPresent(logicalOperationTypeEnum ->
                        com.shouqianba.cua.utils.collection.MapUtils.ifPresent(logicalOperationFunctionMap, logicalOperationTypeEnum,
                                biFunction -> checkResult.set(biFunction.apply(merchantFeaturePropertyBO, validatedRuleRequiredPropertyValue)))
                );
        return checkResult.get();
    }



    /**
     * 获取商户对象具体属性值
     *
     * @param merchantFeaturePropertyBO      商户特性及类型
     * @return 商户对象具体属性
     */
    private String getMerchantFeatureProperty(MerchantFeaturePropertyBO merchantFeaturePropertyBO) {
        return getMerchantFeatureProperty(merchantFeaturePropertyBO.getMerchantFeatureBO(), merchantFeaturePropertyBO.getObjectPropertyType());
    }


    /**
     * 获取商户对象具体属性值
     *
     * @param merchantFeatureBO  商户特征
     * @param objectPropertyType 对象属性
     * @return 商户对象具体属性值
     */
    private String getMerchantFeatureProperty(MerchantFeatureBO merchantFeatureBO, String objectPropertyType) {
        try {
            return BeanUtils.getProperty(merchantFeatureBO, objectPropertyType);
        } catch (IllegalAccessException e) {
            log.error("获取商户属性异常,访问、调用或修改未经允许的内容.商户属性:{}", objectPropertyType);
        } catch (InvocationTargetException e) {
            log.error("调用商户属性目标方法(getXxx)时出现异常,商户属性:{}", objectPropertyType);
        } catch (NoSuchMethodException e) {
            log.error("未找到对应的商户属性调用方法,商户属性:{}", objectPropertyType);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 校验商户是否符合收单机构的规则
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     * @return 校验结果
     */
    @Override
    public ContractGroupRuleVerifyResultBO checkMerchantEligibilityToAcquirer(String merchantSn, String acquirer) {
        if (StringUtils.isBlank(merchantSn) || StringUtils.isBlank(acquirer)) {
            throw new CommonPubBizException("参数异常,商户号: " + merchantSn + "收单机构: " + acquirer);
        }
        MerchantFeatureBO merchantFeature = getMerchantFeature(merchantSn, null);
        log.info("checkMerchantEligibilityToAcquirer, 商户特征: {}", merchantFeature);
        return checkEligibilityByAcquirer(acquirer, merchantFeature).orElseGet(() -> checkEligibilityByUnableRule(merchantFeature, acquirer));
    }

    private Optional<ContractGroupRuleVerifyResultBO> checkEligibilityByAcquirer(String acquirer, MerchantFeatureBO merchantFeature) {
        Optional<AcquirerSharedAbility> sharedAbilityByAcquirer = acquirerFacade.getSharedAbilityByAcquirer(acquirer);
        if (sharedAbilityByAcquirer.isPresent()) {
            ContractGroupRuleVerifyResultBO contractGroupRuleVerifyResultBO = sharedAbilityByAcquirer.get().checkSatisfactionToAcquirerTemplate(merchantFeature);
            if (contractGroupRuleVerifyResultBO.isCheckFail()) {
                return Optional.of(contractGroupRuleVerifyResultBO);
            }
        }
        return Optional.empty();
    }

    public ContractGroupRuleVerifyResultBO checkEligibilityByUnableRule(MerchantFeatureBO merchantFeature, String acquirer) {
        List<String> groupIds = mcRuleGroupDAO.listByAcquirerAndStatus(acquirer, ValidStatusEnum.VALID.getValue()).stream()
                .map(McRuleGroupDO::getGroupId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupIds)) {
            return ContractGroupRuleVerifyResultBO.fail("收单机构对应的规则组为空");
        }
        Set<Long> groupCombinedStrategyIds = groupCombinedStrategyBiz.listStrategyIdsByTypeAndGroupIds(GroupCombinedTypeEnum.SINGLE_MASTER_NO_BACKUP, groupIds);
        if (CollectionUtils.isEmpty(groupCombinedStrategyIds)) {
            return ContractGroupRuleVerifyResultBO.success();
        }
        List<GroupRouteRulesDecisionDO> topLevelUnableRulesDecisions = listAllValidMcRulesDecisionDOS()
                .stream()
                .filter(t -> Objects.equals(t.getParentId(), 0L)
                        && Objects.equals(t.getChooseType(), ChooseTypeEnum.UNABLE.getValue())
                        && groupCombinedStrategyIds.contains(t.getGroupStrategyId()))
                .sorted(Comparator.comparingInt(GroupRouteRulesDecisionDO::getPriority))
                .collect(Collectors.toList());
        for (GroupRouteRulesDecisionDO topLevelUnableRulesDecision : topLevelUnableRulesDecisions) {
            ContractRuleDecisionEvaluateResultBO decisionEvaluateResultBO = evaluateRuleDecisionNode(merchantFeature, topLevelUnableRulesDecision);
            if (decisionEvaluateResultBO.isPass()) {
                log.warn("商户准入收单机构校验,命中UNABLE规则: {}, {},规则决策明细: {}", merchantFeature.getMerchantSn(), topLevelUnableRulesDecision.getName(), decisionEvaluateResultBO);
                return ContractGroupRuleVerifyResultBO.fail("命中UNABLE规则: " + topLevelUnableRulesDecision.getName() + " " + decisionEvaluateResultBO.getMessage());
            }
        }
        return ContractGroupRuleVerifyResultBO.success();
    }
}
