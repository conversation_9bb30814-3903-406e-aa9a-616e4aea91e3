package com.wosai.upay.job.refactor.task.license;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.AffectPrimaryTaskStatusEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.shouqianba.cua.utils.object.StringExtensionUtils;
import com.shouqianba.service.MerchantContractService;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.shouqianba.withdrawservice.service.WithdrawService;
import com.wosai.upay.bank.info.api.model.BankInfo;
import com.wosai.upay.bank.info.api.service.BankInfoService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.biz.PaymentModeChangeBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.microUpgrade.UpgradeVersion;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.dto.crm.CrmInformationManagementApplyFormDTO;
import com.wosai.upay.job.model.dto.crm.SubmitResultForCrmInfoManageDTO;
import com.wosai.upay.job.model.dto.crm.v3.MicroUpgradeDTO;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.biz.provider.McProviderBiz;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantBankAccountBO;
import com.wosai.upay.job.refactor.model.bo.ScheduleTaskExecutePropertyBO;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.refactor.model.enums.*;
import com.wosai.upay.job.refactor.model.enums.crm.CrmApplyFormAuditStatusEnum;
import com.wosai.upay.job.refactor.service.impl.InterScheduleTaskServiceImpl;
import com.wosai.upay.job.refactor.task.AbstractInternalScheduleTaskHandleTemplate;
import com.wosai.upay.job.refactor.task.license.account.BankAccountChangeTaskHandler;
import com.wosai.upay.job.refactor.task.license.account.BankAccountVerifyTaskHandler;
import com.wosai.upay.job.refactor.task.license.account.ChangeAccountWithLicenseUpdate;
import com.wosai.upay.job.refactor.task.license.crm.CrmLicenseApplyUpdate;
import com.wosai.upay.job.refactor.task.license.entity.*;
import com.wosai.upay.job.refactor.task.license.micro.AbstractUpdateTradeParamsTemplate;
import com.wosai.upay.job.refactor.task.license.micro.MicroUpgradeV3TaskHandler;
import com.wosai.upay.job.refactor.task.license.update.LicenseUpdateToAcquirerTaskHandler;
import com.wosai.upay.job.refactor.utils.DtoConverter;
import com.wosai.upay.job.service.task.BusinessLicenceTaskService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.Tuple2;
import com.wosai.upay.wallet.service.WalletService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 营业执照认证任务V3
 * 和营业执照认证互斥的任务，需要加上type=9
 *
 * <AUTHOR>
 * @date 2025/06/26 11:11
 */
@Component
@Slf4j
public class BusinessLicenceCertificationV3Task extends AbstractInternalScheduleTaskHandleTemplate {

    @Resource
    private ContractStatusDAO contractStatusDAO;

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;


    @Resource
    private AcquirerFacade acquirerFacade;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private ParamContextBiz paramContextBiz;


    @Autowired
    protected BusinessLogBiz businessLogBiz;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private MerchantService merchantService;

    @Resource
    private InterScheduleTaskServiceImpl interScheduleTaskService;


    @Resource(name = "microUpgradeV3TaskHandler")
    private MicroUpgradeV3TaskHandler microUpgradeV3TaskHandler;


    @Autowired
    private CrmLicenseApplyUpdate crmLicenseApplyUpdate;

    @Autowired
    private MerchantBankService merchantBankService;

    public static final String SUB_TASK_TYPE_RE_CONTRACT = "重新入网";

    public static final String SUB_TASK_TYPE_CHANGE_PARAMS = "参数变更";

    public static final String SUB_TASK_TYPE_PAYWAY_AUTH = "支付源实名认证";

    public static final String SUB_TASK_TYPE_UPDATE_LICENSE = "营业执照更新";

    public static final String SUB_TASK_TYPE_ACCOUNT_VERIFY = "结算账户校验";

    public static final String SUB_TASK_TYPE_ACCOUNT_CHANGE = "结算账户变更";

    private static final String UPDATE_ACCOUNT_KEY = "account";

    @Resource
    private BankAccountVerifyTaskHandler bankAccountVerifyTaskHandler;

    @Autowired
    private BankInfoService bankInfoService;

    @Resource
    private LicenseUpgradeErrorMsgPromptConvertor errorMsgPromptConvertor;

    @Resource
    private BankAccountChangeTaskHandler bankAccountChangeTaskHandler;

    @Resource
    private ChangeAccountWithLicenseUpdate changeAccountWithLicenseUpdate;
    @Resource
    private PaymentModeChangeBiz paymentModeChangeBiz;

    @Resource
    private McProviderBiz mcProviderBiz;


    @Override
    public InternalScheduleTaskTypeEnum getTaskType() {
        return InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V3;
    }


    @Override
    protected ScheduleTaskExecutePropertyBO getTaskExecuteProperty() {
        ScheduleTaskExecutePropertyBO executeProperty = new ScheduleTaskExecutePropertyBO();
        executeProperty.setBatchGetPatternType(BatchGetScheduleTaskPatternTypeEnum.PENDING_AND_WAITING_EXT_TASKS);
        executeProperty.setSupportParallel(true);
        return executeProperty;
    }



    @Override
    protected InternalScheduleSubTaskProcessResultBO handleSingleSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        try {
            if (isAccountVerifySubTask(subTaskDO)) {
                return bankAccountVerifyTaskHandler.handleAccountVerify(mainTaskDO, subTaskDO);
            }
            boolean microUpgradeTask = isMicroUpgradeTask(mainTaskDO);
            if (microUpgradeTask) {
                return microUpgradeV3TaskHandler.handleMicroUpgrade(mainTaskDO, subTaskDO);
            }
            return InternalScheduleSubTaskProcessResultBO.fail("当前主任务不是小微升级AT一致性任务");
        } catch (Exception e) {
            log.error("BusinessLicenceCertificationV3Task handleSingleSubTask error, merchantSn:{}", mainTaskDO.getMerchantSn(), e);
            // 兜底，防止异常堆栈展示到app
            mainTaskDO.setResult("系统异常");
            return InternalScheduleSubTaskProcessResultBO.fail(e.getMessage());
        }

    }


    private boolean isAccountVerifySubTask(InternalScheduleSubTaskDO subTaskDO) {
        return StringUtils.equals(subTaskDO.getTaskType(), SUB_TASK_TYPE_ACCOUNT_VERIFY);
    }

    /**
     * 主任务执行完后的后置操作
     */
    @Override
    protected void mainTaskPostProcessor(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> sortedSubTasks) {
        BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(),
                BusinessLicenseCertificationV3MainTaskContext.class);
        Integer fieldAppInfoId = mainTaskContextBOInner.getAuditApplyDTO().getFieldAppInfoId();
        if (mainTaskDO.taskSuccess()) {
            crmLicenseApplyUpdate.updateCrmLicenseApplyStatus(fieldAppInfoId, CrmApplyFormAuditStatusEnum.AUDIT_SUCCESS.getValue(), null);
            submitAccountChangeTaskForNotSupportedOneAsk(mainTaskDO);
            deleteTempBusinessLicenseAndMerchant(mainTaskDO.getMerchantSn());
            syncBankAccountCertificate(mainTaskContextBOInner, mainTaskDO.getMerchantSn());
        }
        if (mainTaskDO.taskFail()) {
            String failMsg = errorMsgPromptConvertor.getUpgradeCheckFailPromptMessageMapJson( mainTaskDO.getResult(), LicenseUpgradeErrorMsgPromptConvertor.DEFAULT_MESSAGE);
            crmLicenseApplyUpdate.updateCrmLicenseApplyStatus(fieldAppInfoId, CrmApplyFormAuditStatusEnum.AUDIT_FAIL.getValue(), failMsg);
        }
        if (mainTaskDO.taskSuccess() && mainTaskContextBOInner.getMicroUpgrade()) {
            // 品牌支付模式商户重新挂靠
            paymentModeChangeBiz.tryChangePaymentModeAfterMicroUpgrade(mainTaskDO.getMerchantSn());
        }
    }


    /**
     * 如果法人对私，且本次执照变更不涉及银行卡信息确认，同步证件信息到结算
     */
    private void syncBankAccountCertificate(BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner, String merchantSn) {
        try {
            Boolean needUpdateBankAccount = mainTaskContextBOInner.getNeedUpdateBankAccount();
            if (needUpdateBankAccount) {
                return;
            }
            String merchantId = merchantBasicInfoBiz.getMerchantInfoBySn(merchantSn).getId();
            BusinessLicenseAuditApplyDTO auditApplyDTO = mainTaskContextBOInner.getAuditApplyDTO();
            BankAccountCertificateBO bankAccountCertificateBO = getBankAccountCertificateBO(auditApplyDTO);
            changeAccountWithLicenseUpdate.syncBankAccountCertificateUnnecessaryInfo(merchantId, bankAccountCertificateBO, auditApplyDTO.getPlatform());
        } catch (Exception e) {
            log.error("syncBankAccountCertificate error, merchantSn: {}", merchantSn, e);
        }
    }

    private BankAccountCertificateBO getBankAccountCertificateBO(BusinessLicenseAuditApplyDTO auditApplyDTO) {
        BusinessLicenseDTO businessLicense = auditApplyDTO.getBusinessLicense();
        BankAccountCertificateBO bankAccountCertificateBO = new BankAccountCertificateBO();
        bankAccountCertificateBO.setCertificateType(businessLicense.getLegalPersonCertificateType());
        bankAccountCertificateBO.setCertificateNum(businessLicense.getLegalPersonCertificateNumber());
        bankAccountCertificateBO.setCertificateAddress(businessLicense.getLegalPersonCertificateAddress());
        bankAccountCertificateBO.setCertificateIssuingAuthority(businessLicense.getLegalPersonCertificateIssuingAuthority());
        bankAccountCertificateBO.setCertificateFrontPhoto(businessLicense.getLegalPersonCertificateFrontPhoto());
        bankAccountCertificateBO.setCertificateBackPhoto(businessLicense.getLegalPersonCertificateBackPhoto());
        bankAccountCertificateBO.setCertificateValidity(businessLicense.getLegalPersonCertificateValidity());
        return bankAccountCertificateBO;
    }

    private void deleteTempBusinessLicenseAndMerchant(String merchantSn) {
        try {
            Optional<String> merchantOpt = merchantBasicInfoBiz.getMerchantIdByMerchantSn(merchantSn);
            if (!merchantOpt.isPresent()) {
                log.warn("商户不存在, merchantSn:{}", merchantSn);
                return;
            }
            String merchantId = merchantOpt.get();
            merchantBusinessLicenseService.delMcMerchantBusinessLicense(merchantId);
            merchantService.delMcMerchant(merchantId);
        } catch (Exception e) {
            log.warn("删除营业执照或者商户中间表错误, merchantSn:{}", merchantSn, e);
        }

    }

    /**
     * 如果是当前收单机构不支持一个请求同时变更营业执照同时变更结算账户，需要插入一个结算账户变更任务
     */
    private void submitAccountChangeTaskForNotSupportedOneAsk(InternalScheduleMainTaskDO mainTaskDO) {
        try {
            BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(),
                    BusinessLicenseCertificationV3MainTaskContext.class);
            // 小微升级后未重新入网的其他收单机构参数本身就禁用了，不需要再做结算信息的更新了
            if (mainTaskContextBOInner.getMicroUpgrade() || !mainTaskContextBOInner.getNeedUpdateBankAccount()) {
                return;
            }
            // 非当前收单机构（当前的收单机构已经做过了结算信息变更），且营业执照变更任务没法同时变更结算信息，需要再插入一个结算信息变更的任务
            List<String> allContractedExceptInUseAcquirers = merchantTradeParamsBiz.listParamsByMerchantSn(mainTaskDO.getMerchantSn()).stream()
                    .filter(t -> Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue())
                            && Objects.equals(t.getDeleted(), Deleted.NO_DELETED.getValue()))
                    .map(t -> StringExtensionUtils.toSafeString(mcProviderBiz.getAcquirerByProvider(t.getProvider().toString())))
                    .filter(t -> StringUtils.isNotBlank(t) && !StringUtils.equals(t, mainTaskDO.getAcquirer()))
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allContractedExceptInUseAcquirers)) {
                return;
            }
            for (String needChangeAccountAcquirer : allContractedExceptInUseAcquirers) {
                if (bankAccountChangeTaskHandler.isLicenseTaskNeedExtraChangeAccountTask(needChangeAccountAcquirer, mainTaskDO)) {
                    log.info("当前收单机构不支持营业执照和结算账户同时变更,单独生成结算账户变更任务 merchantSn:{}, acquirer:{}", mainTaskDO.getMerchantSn(), needChangeAccountAcquirer);
                    bankAccountChangeTaskHandler.doSubmitChangeAccountContractTask(mainTaskDO, needChangeAccountAcquirer);
                }
            }
        } catch (Exception e) {
            log.warn("submitAccountChangeTaskForNotSupportedOneAsk error, merchantSn:{}", mainTaskDO.getMerchantSn(), e);
        }
    }

    private Map<String, Object> getContextMapByMainTaskDO(InternalScheduleMainTaskDO mainTaskDO) {
        BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(),
                BusinessLicenseCertificationV3MainTaskContext.class);
        Map<String, Object> contextMap = avro.shaded.com.google.common.collect.Maps.newHashMap();
        contextMap.put(ParamContextBiz.KEY_MERCHANT, mainTaskContextBOInner.getMerchant());
        contextMap.put(ParamContextBiz.KEY_BANK_ACCOUNT, mainTaskContextBOInner.getBankAccount());
        contextMap.put(ParamContextBiz.KEY_BANK_INFO, mainTaskContextBOInner.getBankInfo());
        contextMap.put(ParamContextBiz.KEY_BUSINESS_LICENCE, mainTaskContextBOInner.getMerchantBusinessLicense());
        return contextMap;
    }

    private Optional<AbstractUpdateTradeParamsTemplate> getLicenceCertificationAcquirerHandlerOpt(String acquirer) {
        return  applicationContext.getBeansOfType(AbstractUpdateTradeParamsTemplate.class).values().stream()
                .filter(handler -> StringUtils.equals(handler.getAcquirer(), acquirer))
                .findFirst();
    }


    private List<InternalScheduleSubTaskDO> buildAllSubTasksForMicroUpgrade(List<String> reContractAcquirers, InternalScheduleMainTaskDO mainTask, int priority) {
        List<InternalScheduleSubTaskDO> scheduleSubTaskDOS = Lists.newArrayListWithCapacity(reContractAcquirers.size());
        Map<String/*acquirer*/, Map<String/*payWay*/, List<MerchantProviderParamsDO>>> acquirerParams = merchantTradeParamsBiz.getMerchantAcquirerParamsMap(mainTask.getMerchantSn());
        for (String reContractAcquirer : reContractAcquirers) {
            if (!acquirerParams.containsKey(reContractAcquirer)) {
                log.warn("商户缺少交易参数,merchantSn={},acquirer={}", mainTask.getMerchantSn(), reContractAcquirer);
                continue;
            }
            Map<String, List<MerchantProviderParamsDO>> payWayParams = acquirerParams.get(reContractAcquirer);
            if (!payWayParams.containsKey(PaywayEnum.ACQUIRER.getValue().toString())) {
                log.warn("商户缺少收单机构交易参数,merchantSn={},acquirer={}", mainTask.getMerchantSn(), reContractAcquirer);
                continue;
            }
            buildReContractSubTask(mainTask, reContractAcquirer, priority, payWayParams).ifPresent(scheduleSubTaskDOS::add);
            priority++;
        }
        //支付源实名认证任务
        scheduleSubTaskDOS.add(buildPayWayAuthTask(mainTask, priority));
        priority++;
        scheduleSubTaskDOS.add(buildChangeParamsSubTask(mainTask, priority));
        return scheduleSubTaskDOS;
    }

    private Optional<InternalScheduleSubTaskDO> buildReContractSubTask(InternalScheduleMainTaskDO mainTask, String reContractAcquirer, int priority,
                                                                       Map<String, List<MerchantProviderParamsDO>> payWayParams) {
        Optional<AcquirerSharedAbility> acquirerAbilityOpt = acquirerFacade.getSharedAbilityByAcquirer(reContractAcquirer);
        if (!acquirerAbilityOpt.isPresent()) {
            log.warn("收单机构不支持, acquirer:{}", reContractAcquirer);
            return Optional.empty();
        }
        final BusinessLicenseCertificationV3MainTaskContext businessLicenseCertificationV3MainTaskContext = JSON.parseObject(mainTask.getContext(),
                BusinessLicenseCertificationV3MainTaskContext.class);
        //当前任务对应的收单机构和正在使用的收单机构是否一致

        AcquirerSharedAbility acquirerSharedAbility = acquirerAbilityOpt.get();
        InternalScheduleSubTaskDO reContractSubTask = new InternalScheduleSubTaskDO();
        reContractSubTask.setPriority(priority);
        reContractSubTask.setMerchantSn(mainTask.getMerchantSn());
        reContractSubTask.setType(getTaskType().getValue());
        reContractSubTask.setTaskType(SUB_TASK_TYPE_RE_CONTRACT);
        reContractSubTask.setMainTaskId(mainTask.getId());
        reContractSubTask.setAcquirer(reContractAcquirer);
        StringExtensionUtils.ifNotBlank(acquirerSharedAbility.getAcquirerInfo().getProvider(), t -> reContractSubTask.setProvider(Integer.valueOf(t)));
        reContractSubTask.setAffectMainTaskStatus(Objects.equals(businessLicenseCertificationV3MainTaskContext.getOldInUseAcquirer(),reContractAcquirer)
                ? AffectPrimaryTaskStatusEnum.YES.getValue()
                : AffectPrimaryTaskStatusEnum.NO.getValue());
        reContractSubTask.setRemark("小微升级V3重新向收单机构进件");
        SubTaskContextBOInner subTaskContextBOInner = new SubTaskContextBOInner();
        // 走到这里 payWayParams必定包含了收单机构，微信，支付宝的参数
        subTaskContextBOInner.setOldPayWayParamsMap(payWayParams);
        String oldUnionMerchantId = acquirerSharedAbility.getUnionMerchantId(
                CollectionUtils.isEmpty(payWayParams.get(PaywayEnum.UNIONPAY.getValue().toString())) ? null : payWayParams.get(PaywayEnum.UNIONPAY.getValue().toString()).get(0),
                payWayParams.get(PaywayEnum.ACQUIRER.getValue().toString()).get(0));
        subTaskContextBOInner.setOldUnionMerchantId(oldUnionMerchantId);
        subTaskContextBOInner.setOldAcquirerMerchantId(payWayParams.get(PaywayEnum.ACQUIRER.getValue().toString()).get(0).getPayMerchantId());
        getLicenceCertificationAcquirerHandlerOpt(reContractAcquirer)
                .ifPresent(handler -> subTaskContextBOInner.setOldMerchantTermNo(handler.getContractTermNo(mainTask.getMerchantSn())));
        reContractSubTask.setContext(JSON.toJSONString(subTaskContextBOInner));
        return Optional.of(reContractSubTask);
    }





    private InternalScheduleSubTaskDO buildChangeParamsSubTask(InternalScheduleMainTaskDO mainTask, int priority) {
        InternalScheduleSubTaskDO changeParamsSubTask = new InternalScheduleSubTaskDO();
        changeParamsSubTask.setType(getTaskType().getValue());
        changeParamsSubTask.setMerchantSn(mainTask.getMerchantSn());
        changeParamsSubTask.setTaskType(SUB_TASK_TYPE_CHANGE_PARAMS);
        changeParamsSubTask.setPriority(priority);
        changeParamsSubTask.setMainTaskId(mainTask.getId());
        changeParamsSubTask.setAcquirer(mainTask.getAcquirer());
        changeParamsSubTask.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        changeParamsSubTask.setRemark("等待实名授权子任务完成后，切换参数");
        return changeParamsSubTask;
    }


    /**
     *
     * @param mainTask
     * @param priority
     * @return
     */
    private InternalScheduleSubTaskDO buildPayWayAuthTask(InternalScheduleMainTaskDO mainTask, int priority) {
        InternalScheduleSubTaskDO changeParamsSubTask = new InternalScheduleSubTaskDO();
        changeParamsSubTask.setType(getTaskType().getValue());
        changeParamsSubTask.setMerchantSn(mainTask.getMerchantSn());
        changeParamsSubTask.setTaskType(SUB_TASK_TYPE_PAYWAY_AUTH);
        changeParamsSubTask.setPriority(priority);
        changeParamsSubTask.setAcquirer(mainTask.getAcquirer());
        changeParamsSubTask.setMainTaskId(mainTask.getId());
        changeParamsSubTask.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        changeParamsSubTask.setRemark("等待重新进件子任务完成后，检查支付源实名状态");
        return changeParamsSubTask;
    }

    @Resource
    @Lazy
    private BusinessLicenceTaskService  businessLicenceTaskService;

    @Resource
    private SubBizParamsBiz subBizParamsBiz;

    /**
     * V3版本小微升级要求AT也要报备并且授权而且只有在授权完成才可以进行切换参数
     */
    public Tuple2<List<String>, String> listNeedToReContractAcquirers(String merchantSn, String inUseAcquirer) {
        List<String> reContractAcquirers = Lists.newArrayList();

        if(!isMicroUpgradeV3(merchantSn)) {
            return new Tuple2<>(reContractAcquirers,"当前小微升级V3版本关闭,请重新申请升级");
        }
        reContractAcquirers.add(inUseAcquirer);  // 先添加当前使用的收单机构，确保其在第一位

        // 再添加其他间连收单机构，自动去重
        List<String> indirectAcquirers = subBizParamsBiz.getContractIndirectAcquireList(merchantSn);
        reContractAcquirers.addAll(indirectAcquirers.stream()
                .filter(acquirer -> !Objects.equals(acquirer, inUseAcquirer))
                .collect(Collectors.toList()));

        return new Tuple2<>(reContractAcquirers, null);

    }


    /**
     * 提交营业执照更新任务
     *
     * @param applyReq    营业执照审批单
     */
    public SubmitResultForCrmInfoManageDTO insertTask(CrmInformationManagementApplyFormDTO applyReq) {
        Optional<String> snOpt = merchantBasicInfoBiz.getMerchantSnByMerchantId(applyReq.getMerchantId());
        if (!snOpt.isPresent()) {
            return SubmitResultForCrmInfoManageDTO.fail("商户不存在");
        }
        String merchantSn = snOpt.get();
        BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO = convertToBusinessLicenseAuditApplyDTO(applyReq);
        doInsertTaskByLicenseAuditApply(merchantSn, applyReq.getFieldAppInfoId(), businessLicenseAuditApplyDTO);
        return SubmitResultForCrmInfoManageDTO.success();
    }

    private boolean needUpdateBankAccount(BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO) {
        return Objects.nonNull(businessLicenseAuditApplyDTO) && Objects.nonNull(businessLicenseAuditApplyDTO.getBankAccount());
    }

    public void doInsertTaskByLicenseAuditApply(String merchantSn, Integer fieldAppInfoId, BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO) {
        boolean isMicroUpgrade = Boolean.TRUE;
        InternalScheduleMainTaskDO mainTask = buildMainTask(merchantSn, isMicroUpgrade, businessLicenseAuditApplyDTO, fieldAppInfoId);
        int priority = 0;
        int subTaskNum = 0;
        InternalScheduleSubTaskDO accountVerifySubTask;
        if (needUpdateBankAccount(businessLicenseAuditApplyDTO)) {
            priority++;
            subTaskNum++;
            accountVerifySubTask = buildAccountVerifyTask(mainTask);
        } else {
            accountVerifySubTask = null;
        }
        priority++;
        List<InternalScheduleSubTaskDO> otherSubTasks = buildOtherSubTasks(isMicroUpgrade, businessLicenseAuditApplyDTO, mainTask, priority);
        subTaskNum = subTaskNum + (int) otherSubTasks.stream().filter(t -> Objects.equals(t.getAffectMainTaskStatus(), AffectPrimaryTaskStatusEnum.YES.getValue())).count();
        mainTask.setAffectStatusSubTaskNum(subTaskNum);
        if (Objects.nonNull(accountVerifySubTask)) {
            mainTask.setEnableScheduledStatus(ScheduleEnum.SCHEDULE_DISABLE.getValue());
            interScheduleTaskService.insertTasksWithExpireTime(mainTask, Collections.singletonList(accountVerifySubTask));
            otherSubTasks.forEach(internalScheduleSubTaskDO -> {
                internalScheduleSubTaskDO.setMainTaskId(mainTask.getId());
                //这里先把所有的子任务依赖的任务都变成账户验证任务
                internalScheduleSubTaskDO.setDependOnSubTaskId(accountVerifySubTask.getId());
            });
            mainTask.setEnableScheduledStatus(ScheduleEnum.SCHEDULE_ENABLE.getValue());
                //当前逻辑是：对于多通道商户必须等待当前正在使用的通道入网成功才可以开始调度,如果当前商户有AT，则需要等待AT授权完成，然后才能进行切换参数,所以主要流程是主通道->副通道->主通道实名授权->切换参数
            internalScheduleMainTaskDAO.updateMainTaskAndInsertSubTasks(mainTask, otherSubTasks);
        } else {
            interScheduleTaskService.insertTasksWithExpireTime(mainTask, otherSubTasks);
        }
    }

    public BusinessLicenseAuditApplyDTO convertToBusinessLicenseAuditApplyDTO(CrmInformationManagementApplyFormDTO applyReq) {
        if (Objects.isNull(applyReq) || CollectionUtils.isEmpty(applyReq.extractCrmFormFieldInfoDTOsFromDevParams())) {
            throw new ContractBizException("营业执照申请单内容为空");
        }
        BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO = new BusinessLicenseAuditApplyDTO();
        businessLicenseAuditApplyDTO.setSubmitUserId(applyReq.getUserId());
        businessLicenseAuditApplyDTO.setSubmitUserName(applyReq.getUserName());
        businessLicenseAuditApplyDTO.setPlatform(applyReq.getPlatform());
        businessLicenseAuditApplyDTO.setFieldAppInfoId(applyReq.getFieldAppInfoId());
        businessLicenseAuditApplyDTO.setBusinessLicense(DtoConverter.convertToDTOBySourceKeyValueList(applyReq.extractCrmFormFieldInfoDTOsFromDevParams(), BusinessLicenseDTO.class));
        if (MapUtils.isNotEmpty(applyReq.getBusinessAppInfo()) && applyReq.getBusinessAppInfo().containsKey(UPDATE_ACCOUNT_KEY)) {
            businessLicenseAuditApplyDTO.setBankAccount(JSON.parseObject(JSON.toJSONString(applyReq.getBusinessAppInfo().get(UPDATE_ACCOUNT_KEY)), BankAccountDTO.class));
        }
        businessLicenseAuditApplyDTO.basePhotos();
        return businessLicenseAuditApplyDTO;
    }


    private InternalScheduleSubTaskDO buildAccountVerifyTask(InternalScheduleMainTaskDO mainTask) {
        InternalScheduleSubTaskDO updateTask = new InternalScheduleSubTaskDO();
        updateTask.setType(getTaskType().getValue());
        updateTask.setMerchantSn(mainTask.getMerchantSn());
        updateTask.setTaskType(SUB_TASK_TYPE_ACCOUNT_VERIFY);
        updateTask.setMainTaskId(mainTask.getId());
        updateTask.setPriority(1);
        updateTask.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        updateTask.setRemark(SUB_TASK_TYPE_ACCOUNT_VERIFY);
        return updateTask;
    }

    private InternalScheduleSubTaskDO buildJustUpdateLicenseTaskSubTask(InternalScheduleMainTaskDO mainTask, Integer priority) {
        InternalScheduleSubTaskDO updateTask = new InternalScheduleSubTaskDO();
        updateTask.setType(getTaskType().getValue());
        updateTask.setMerchantSn(mainTask.getMerchantSn());
        updateTask.setTaskType(SUB_TASK_TYPE_UPDATE_LICENSE);
        updateTask.setMainTaskId(mainTask.getId());
        updateTask.setPriority(priority);
        updateTask.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        updateTask.setRemark(SUB_TASK_TYPE_UPDATE_LICENSE);
        return updateTask;
    }


    private List<InternalScheduleSubTaskDO> buildOtherSubTasks(boolean isMicroUpgrade,
                                                               BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO,
                                                               InternalScheduleMainTaskDO mainTask, int priority) {
        String merchantSn = mainTask.getMerchantSn();
        Tuple2<List<String>, String> reContractAcquirers = listNeedToReContractAcquirers(merchantSn, mainTask.getAcquirer());
        if (CollectionUtils.isEmpty(reContractAcquirers.get_1())) {
            log.error("营业执照升级，获取需要重新进件的收单机构为空, merchantSn:{}, taskId:{}, reason:{}", merchantSn, mainTask.getId(), reContractAcquirers.get_2());
            throw new ContractBizException("营业执照升级，获取需要重新进件的收单机构为空，" + reContractAcquirers.get_2());
        }
        //构建所有子任务
        List<InternalScheduleSubTaskDO> scheduleSubTaskDOS = buildAllSubTasksForMicroUpgrade(reContractAcquirers.get_1(), mainTask, priority);
        if (CollectionUtils.isEmpty(scheduleSubTaskDOS) || scheduleSubTaskDOS.size() == 1) {
            log.error("营业执照升级，重新入网子任务为空, merchantSn:{}, taskId:{}", merchantSn, mainTask.getId());
            throw new ContractBizException("营业执照升级，构建重新入网子任务为空");
        }
        return scheduleSubTaskDOS;

    }

    private InternalScheduleSubTaskDO buildAccountChangeTask(InternalScheduleMainTaskDO mainTask, int priority) {
        InternalScheduleSubTaskDO updateTask = new InternalScheduleSubTaskDO();
        updateTask.setType(getTaskType().getValue());
        updateTask.setMerchantSn(mainTask.getMerchantSn());
        updateTask.setTaskType(SUB_TASK_TYPE_ACCOUNT_CHANGE);
        updateTask.setMainTaskId(mainTask.getId());
        updateTask.setPriority(priority);
        updateTask.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        updateTask.setRemark(SUB_TASK_TYPE_ACCOUNT_CHANGE);
        return updateTask;
    }


    private InternalScheduleMainTaskDO buildMainTask(String merchantSn, boolean isMicroUpgrade, BusinessLicenseAuditApplyDTO applyReq, Integer fieldAppInfoId) {
        InternalScheduleMainTaskDO mainTask = new InternalScheduleMainTaskDO();
        mainTask.setMerchantSn(merchantSn);
        mainTask.setType(getTaskType().getValue());
        Optional<ContractStatusDO> contractStatus = contractStatusDAO.getByMerchantSn(merchantSn);
        if (!contractStatus.isPresent()
                || !Objects.equals(contractStatus.get().getStatus(), ContractStatus.STATUS_SUCCESS)
                || StringUtils.isBlank(contractStatus.get().getAcquirer())) {
            log.warn("商户未进件成功或未关联收单机构, merchantSn:{}", merchantSn);
            throw new ContractBizException("尚未开通间连扫码");
        }
        mainTask.setAcquirer(contractStatus.get().getAcquirer());
        mainTask.setEnableScheduledStatus(ScheduleEnum.SCHEDULE_ENABLE.getValue());
        mainTask.setAffectStatusSubTaskNum(1);
        mainTask.setLastScheduledTime(new Timestamp(System.currentTimeMillis()));
        BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner = new BusinessLicenseCertificationV3MainTaskContext(applyReq);
        mainTaskContextBOInner.setMicroUpgrade(isMicroUpgrade);
        mainTaskContextBOInner.setOldInUseAcquirer(contractStatus.get().getAcquirer());
        mainTaskContextBOInner.setShowTaskOnSpa(isMicroUpgrade);
        mainTaskContextBOInner.setNeedUpdateBankAccount(applyReq.getBankAccount() != null);
        mainTaskContextBOInner.setNeedInsertPayForTask(applyReq.getBankAccount() != null);
        mainTaskContextBOInner.setNeedUpdateBusinessName(needUpdateBusinessName(merchantSn, applyReq.getBusinessLicense().getBusinessName()));
        mainTaskContextBOInner.setAuditApplyDTO(applyReq);
        populateVerifyStatus(mainTask.getMerchantSn(), mainTaskContextBOInner);
        merchantTradeParamsBiz.getAcquirerMerchantId(merchantSn, contractStatus.get().getAcquirer()).ifPresent(mainTaskContextBOInner::setOldInUseAcquirerMerchantId);
        mainTask.setContext(JSON.toJSONString(mainTaskContextBOInner));
        populateContext(mainTask);
        return mainTask;
    }

    private boolean needUpdateBusinessName(String merchantSn, String businessName) {
        if (StringUtils.isBlank(businessName)) {
            return false;
        }
        return !StringUtils.equals(businessName, merchantBasicInfoBiz.getMerchantInfoBySn(merchantSn).getBusiness_name());
    }

    @Autowired
    private com.wosai.upay.core.service.MerchantService coreBMerchantService;

    private void populateVerifyStatus(String merchantSn,BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner) {
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(merchantSn);
        Map existedDefaultAccount = coreBMerchantService.getMerchantBankAccountByMerchantId(merchantInfo.getId());
        if (MapUtils.isEmpty(existedDefaultAccount)) {
            throw new ContractBizException("商户尚未录入银行卡");
        }
        mainTaskContextBOInner.setOriginalDefaultAccountNumber(MapUtils.getString(existedDefaultAccount, MerchantBankAccount.NUMBER));
        mainTaskContextBOInner.setOriginalDefaultAccountVerifyStatus(MapUtils.getInteger(existedDefaultAccount, MerchantBankAccount.VERIFY_STATUS));
        mainTaskContextBOInner.setOriginalDefaultAccountHolder(MapUtils.getString(existedDefaultAccount, MerchantBankAccount.HOLDER));
        mainTaskContextBOInner.setOriginalDefaultAccountType(MapUtils.getInteger(existedDefaultAccount, MerchantBankAccount.TYPE));
        mainTaskContextBOInner.setOriginalDefaultAccountIdentity(MapUtils.getString(existedDefaultAccount, MerchantBankAccount.IDENTITY));
        Map existedPreAccount = merchantBankService.getMerchantBankAccountPreByMerchantIdAndNumber(merchantInfo.getId(),
                MapUtils.getString(existedDefaultAccount, MerchantBankAccount.NUMBER));
        mainTaskContextBOInner.setOriginalDefaultPreAccountVerifyStatus(MapUtils.getInteger(existedPreAccount, MerchantBankAccountPre.VERIFY_STATUS));
    }

    private void populateContext(InternalScheduleMainTaskDO mainTask) {
        Map<String, Object> paramsContextMap = buildContractContext(mainTask);
        BusinessLicenseCertificationV3MainTaskContext contextBOInner = JSON.parseObject(mainTask.getContext(), BusinessLicenseCertificationV3MainTaskContext.class);
        contextBOInner.setMerchant((Map<String, Object>) MapUtils.getObject(paramsContextMap, ParamContextBiz.KEY_MERCHANT));
        contextBOInner.setBankAccount((Map<String, Object>) MapUtils.getObject(paramsContextMap, ParamContextBiz.KEY_BANK_ACCOUNT));
        contextBOInner.setBankInfo((Map<String, Object>) MapUtils.getObject(paramsContextMap, ParamContextBiz.KEY_BANK_INFO));
        contextBOInner.setMerchantBusinessLicense((Map<String, Object>) MapUtils.getObject(paramsContextMap, ParamContextBiz.KEY_BUSINESS_LICENCE));
        mainTask.setContext(JSON.toJSONString(contextBOInner));
    }



    public Map<String, Object> buildContractContext(InternalScheduleMainTaskDO mainTaskDO) {
        BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV3MainTaskContext.class);
        if (Objects.isNull(mainTaskContextBOInner) || Objects.isNull(mainTaskContextBOInner.getAuditApplyDTO())) {
            throw new ContractBizException("营业执照信息为空");
        }
        Map<String, Object> contextMap = paramContextBiz.getParamContextByMerchantSn(mainTaskDO.getMerchantSn());
        Map<String, Object> merchantMap = (Map<String, Object>) contextMap.get(ParamContextBiz.KEY_MERCHANT);
        merchantMap.put(Merchant.BUSINESS_NAME, mainTaskContextBOInner.getAuditApplyDTO().getBusinessLicense().getBusinessName());
        contextMap.put(ParamContextBiz.KEY_MERCHANT, merchantMap);
        Map<String, Object> originalLicenceMap;
        if (!contextMap.containsKey(ParamContextBiz.KEY_BUSINESS_LICENCE)) {
            originalLicenceMap = new HashMap<>();
        } else {
            originalLicenceMap = (Map<String, Object>) contextMap.get(ParamContextBiz.KEY_BUSINESS_LICENCE);
        }
        BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO = mainTaskContextBOInner.getAuditApplyDTO();
        BusinessLicenseDTO businessLicense = businessLicenseAuditApplyDTO.getBusinessLicense();
        originalLicenceMap.put(MerchantBusinessLicence.MERCHANT_ID, merchantMap.get(DaoConstants.ID));
        originalLicenceMap.put(MerchantBusinessLicence.PHOTO, businessLicense.getLicensePhoto());
        originalLicenceMap.put(MerchantBusinessLicence.NUMBER, businessLicense.getNumber());
        originalLicenceMap.put(MerchantBusinessLicence.NAME, businessLicense.getName());
        originalLicenceMap.put(MerchantBusinessLicence.VALIDITY, businessLicense.getValidity());
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_NAME, businessLicense.getLegalPersonName());
        originalLicenceMap.put(MerchantBusinessLicence.ADDRESS, businessLicense.getRegisterAddress());
        originalLicenceMap.put(MerchantBusinessLicence.TYPE, businessLicense.getType());
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_ID_TYPE, businessLicense.getLegalPersonCertificateType());
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_FRONT_PHOTO, businessLicense.getLegalPersonCertificateFrontPhoto());
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_BACK_PHOTO, businessLicense.getLegalPersonCertificateBackPhoto());
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER, businessLicense.getLegalPersonCertificateNumber());
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_ADDRESS, businessLicense.getLegalPersonCertificateAddress());
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_ISSUING_AUTHORITY, businessLicense.getLegalPersonCertificateIssuingAuthority());
        originalLicenceMap.put(MerchantBusinessLicence.ID_VALIDITY, businessLicense.getLegalPersonCertificateValidity());
        String extraKey = "extra";
        if (StringUtils.isNotEmpty(businessLicense.getAuxiliaryProofMaterials())) {
            if (originalLicenceMap.containsKey(extraKey)) {
                Map extraMap = MapUtils.getMap(originalLicenceMap, extraKey);
                extraMap.put(BusinessLicenseDTO.AUXILIARY_PROOF_MATERIALS_KEY, businessLicense.getAuxiliaryProofMaterials());
                originalLicenceMap.put(extraKey, extraMap);
            } else {
                HashMap<Object, Object> extraMap = Maps.newHashMap();
                extraMap.put(BusinessLicenseDTO.AUXILIARY_PROOF_MATERIALS_KEY, businessLicense.getAuxiliaryProofMaterials());
                originalLicenceMap.put(extraKey, extraMap);
            }

        }
        contextMap.put(ParamContextBiz.KEY_BUSINESS_LICENCE, originalLicenceMap);
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(mainTaskDO.getMerchantSn());
        if (needUpdateBankAccount(businessLicenseAuditApplyDTO)) {
            updateMerchantBankAccount(merchantInfo.getId(), contextMap, businessLicenseAuditApplyDTO);
        }
        updateMerchantBank(merchantInfo.getId(), contextMap);
        return contextMap;
    }

    private void updateMerchantBank(String merchantId, Map<String, Object> contextMap) {
        Map bankInfo = paramContextBiz.getBankInfo(MapUtils.getMap(contextMap, ParamContextBiz.KEY_BANK_ACCOUNT));
        if (MapUtils.isEmpty(bankInfo)) {
            throw new ContractBizException("获取结算账户银行信息失败");
        }
        contextMap.put(ParamContextBiz.KEY_BANK_INFO, bankInfo);
    }

    private void updateMerchantBankAccount(String merchantId, Map<String, Object> contextMap, BusinessLicenseAuditApplyDTO applyDTO) {
        BankAccountDTO bankAccountDTO = applyDTO.getBankAccount();
        BusinessLicenseDTO businessLicense = applyDTO.getBusinessLicense();
        Map existedBankAccount = merchantBankService
                .getMerchantBankAccountPreByMerchantIdAndNumber(merchantId, bankAccountDTO.getAccountNumber());
        MerchantBankAccountBO newBankAccountBO = new MerchantBankAccountBO();
        if (MapUtils.isNotEmpty(existedBankAccount)) {
            newBankAccountBO = JSON.parseObject(JSON.toJSONString(existedBankAccount), MerchantBankAccountBO.class);
            newBankAccountBO.setId(null);  // 避免更新到主表
            newBankAccountBO.setVersion(null);  // fix 版本不一致
        }
        newBankAccountBO.setMerchantId(merchantId);
        newBankAccountBO.setType(bankAccountDTO.calculateTypeBySettlementAccountType());
        Map bankInfo = bankInfoService.getBankInfo(CollectionUtil.hashMap(
                BankInfo.OPENING_NUMBER, bankAccountDTO.getOpeningNumber()
        ));
        if (MapUtils.isEmpty(bankInfo) || StringUtils.isBlank(MapUtils.getString(bankInfo, BankInfo.CLEARING_NUMBER))) {
            throw new ContractBizException("获取结算账户清算银行信息失败");
        }
        newBankAccountBO.setClearingNumber(MapUtils.getString(bankInfo, BankInfo.CLEARING_NUMBER));
        newBankAccountBO.setOpeningNumber(bankAccountDTO.getOpeningNumber());
        newBankAccountBO.setBankCardStatus(ValidStatusEnum.VALID.getValue());
        newBankAccountBO.setBankCardImage(bankAccountDTO.getBankCardPhoto());
        newBankAccountBO.setHolder(bankAccountDTO.getHolder());
        newBankAccountBO.setNumber(bankAccountDTO.getAccountNumber());
        newBankAccountBO.setBankName(bankAccountDTO.getOpeningBank());
        newBankAccountBO.setBranchName(bankAccountDTO.getOpeningBranch());
        newBankAccountBO.setCity(bankAccountDTO.getOpeningCity());
        newBankAccountBO.setCardValidity(bankAccountDTO.getCardValidity());
        newBankAccountBO.setHolderIdBackOcrStatus(ValidStatusEnum.VALID.getValue());
        newBankAccountBO.setHolderIdBackOcrStatus(ValidStatusEnum.VALID.getValue());
        newBankAccountBO.setHolderIdStatus(ValidStatusEnum.VALID.getValue());
        if (Objects.equals(bankAccountDTO.getSettlementAccountType(), BankAccountDTO.LEGAL_PERSONAL)) {
            bankAccountDTO.setCertificateType(businessLicense.getLegalPersonCertificateType());
            bankAccountDTO.setCertificateNumber(businessLicense.getLegalPersonCertificateNumber());
            bankAccountDTO.setCertificateName(businessLicense.getLegalPersonCertificateName());
            bankAccountDTO.setCertificateFrontPhoto(businessLicense.getLegalPersonCertificateFrontPhoto());
            bankAccountDTO.setCertificateBackPhoto(businessLicense.getLegalPersonCertificateBackPhoto());
            bankAccountDTO.setCertificateValidity(businessLicense.getLegalPersonCertificateValidity());
            bankAccountDTO.setCertificateAddress(businessLicense.getLegalPersonCertificateAddress());
            bankAccountDTO.setCertificateIssuingAuthority(businessLicense.getLegalPersonCertificateIssuingAuthority());
        }
        Map extraMap = newBankAccountBO.getExtra();
        if (MapUtils.isEmpty(extraMap)) {
            extraMap = new HashMap<>();
        }
        extraMap.putAll(bankAccountDTO.getExtraMap());
        newBankAccountBO.setExtra(extraMap);
        if (Objects.equals(bankAccountDTO.getSettlementAccountType(), BankAccountDTO.LEGAL_PERSONAL)) {
            newBankAccountBO.setIdType(bankAccountDTO.getCertificateType());
            newBankAccountBO.setIdentity(bankAccountDTO.getCertificateNumber());
            newBankAccountBO.setHolderIdFrontPhoto(bankAccountDTO.getCertificateFrontPhoto());
            newBankAccountBO.setHolderIdBackPhoto(bankAccountDTO.getCertificateBackPhoto());
            newBankAccountBO.setIdValidity(bankAccountDTO.getCertificateValidity());
            newBankAccountBO.setHolderIdCardAddress(bankAccountDTO.getCertificateAddress());
            newBankAccountBO.setHolderIdCardIssuingAuthority(bankAccountDTO.getCertificateIssuingAuthority());
        }
        if (Objects.equals(bankAccountDTO.getSettlementAccountType(), BankAccountDTO.OTHER_PERSONAL)) {
            newBankAccountBO.setIdType(bankAccountDTO.getCertificateType());
            newBankAccountBO.setIdentity(bankAccountDTO.getCertificateNumber());
            newBankAccountBO.setHolderIdFrontPhoto(bankAccountDTO.getCertificateFrontPhoto());
            newBankAccountBO.setHolderIdBackPhoto(bankAccountDTO.getCertificateBackPhoto());
            newBankAccountBO.setIdValidity(bankAccountDTO.getCertificateValidity());
            newBankAccountBO.setHolderIdCardAddress(bankAccountDTO.getCertificateAddress());
            newBankAccountBO.setHolderIdCardIssuingAuthority(bankAccountDTO.getCertificateIssuingAuthority());
            newBankAccountBO.setLetterOfAuthorization(bankAccountDTO.getLetterOfAuthorization());
            newBankAccountBO.setHandLetterOfAuthorization(bankAccountDTO.getHandLetterOfAuthorization());
        }
        // 对公，身份证件信息填充法人的，兼容原有逻辑
        if (Objects.equals(bankAccountDTO.getSettlementAccountType(), BankAccountDTO.CORPORATE)
                || Objects.equals(bankAccountDTO.getSettlementAccountType(), BankAccountDTO.AUTHORIZED_CORPORATE)) {
            newBankAccountBO.setIdType(businessLicense.getLegalPersonCertificateType());
            newBankAccountBO.setIdentity(businessLicense.getLegalPersonCertificateNumber());
            newBankAccountBO.setHolderIdBackPhoto(businessLicense.getLegalPersonCertificateBackPhoto());
            newBankAccountBO.setHolderIdFrontPhoto(businessLicense.getLegalPersonCertificateFrontPhoto());
            newBankAccountBO.setIdValidity(businessLicense.getLegalPersonCertificateValidity());
        }
        if (Objects.equals(bankAccountDTO.getSettlementAccountType(), BankAccountDTO.AUTHORIZED_CORPORATE)) {
            newBankAccountBO.setLetterOfAuthorization(bankAccountDTO.getLetterOfAuthorization());
        }
        contextMap.put(ParamContextBiz.KEY_BANK_ACCOUNT, newBankAccountBO.toMap());
    }

    /**
     * 是否为小微升级主任务
     */
    private boolean isMicroUpgradeTask(InternalScheduleMainTaskDO mainTask) {
        BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTask.getContext(), BusinessLicenseCertificationV3MainTaskContext.class);
        return mainTaskContextBOInner.getMicroUpgrade();
    }

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    /**
     * 根据商户号和收单机构商户号判断该商户是否已经做了小微升级
     *
     * @param merchantSn         商户号
     * @param acquirerMerchantId 收单机构商户号
     * @return true 已做，false 未做
     */
    public boolean isAcquirerMerchantAlreadyUpgrade(String merchantSn, String acquirerMerchantId) {
        List<InternalScheduleMainTaskDO> internalScheduleMainTaskDOS = internalScheduleMainTaskDAO.listBySnAndType(merchantSn, getTaskType().getValue())
                .stream()
                .filter(InternalScheduleMainTaskDO::taskSuccess).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(internalScheduleMainTaskDOS)) {
            return false;
        }
        Optional<MerchantProviderParamsDO> deletedParams = merchantProviderParamsDAO.listDeletedParamsBySn(merchantSn).stream()
                .filter(t -> Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                .filter(t -> StringUtils.equals(t.getPayMerchantId(), acquirerMerchantId) || StringUtils.equals(t.getProviderMerchantId(), acquirerMerchantId))
                .findAny();
        return deletedParams.isPresent();
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubTaskContextBOInner {
        private Map<String/*payWay*/, List<MerchantProviderParamsDO>> oldPayWayParamsMap;  // 新的数据格式
        private List<String> newParamsIdList;
        private List<SubBizParamsDO> oldSubBizParams;
        private String oldUnionMerchantId;
        private String oldAcquirerMerchantId;
        private String oldMerchantTermNo;
        private String accountVerifyBusinessId;
        private Long updateLicenseContractTaskId;
        private Long updateBankAccountContractTaskId;
        /**
         *  小微升级重新进件Id
         */
        private Long contractTaskId;

        public Map<String, List<MerchantProviderParamsDO>> getOldPayWayParamsMap() {
            return Objects.isNull(oldPayWayParamsMap) ? new HashMap<>() : new HashMap<>(oldPayWayParamsMap);
        }

        public List<String> getNewParamsIdList() {
            return Objects.isNull(newParamsIdList) ? new ArrayList<>() : new ArrayList<>(newParamsIdList);
        }

        public List<SubBizParamsDO> getOldSubBizParams() {
            return Objects.isNull(oldSubBizParams) ? new ArrayList<>() : new ArrayList<>(oldSubBizParams);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubTaskRequestDTOInner {
        public SubTaskRequestDTOInner(Map<String, Object> reContractReqMap) {
            this.reContractReqMap = reContractReqMap;
        }

        private Map<String, Object> reContractReqMap;
        private Map<String, Object> queryResultReqMap;

        public Map<String, Object> getReContractReqMap() {
            return Objects.isNull(reContractReqMap) ? new HashMap<>() : new HashMap<>(reContractReqMap);
        }

        public Map<String, Object> getQueryResultReqMap() {
            return Objects.isNull(queryResultReqMap) ? new HashMap<>() : new HashMap<>(queryResultReqMap);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubTaskResponseDTOInner {
        public SubTaskResponseDTOInner(Map<String, Object> reContractRsqMap) {
            this.reContractRsqMap = reContractRsqMap;
        }

        private Map<String, Object> reContractRsqMap;
        private Map<String, Object> queryResultRsqMap;
        private String newUnionMerchantId;
        private String newAcquirerMerchantId;
        private String newTermId;
        private String newShopId;
        private String contractId;
        //每一个进件任务对应的ID
        private Long taskId;

        public Map<String, Object> getReContractRsqMap() {
            return Objects.isNull(reContractRsqMap) ? new HashMap<>() : new HashMap<>(reContractRsqMap);
        }

        public Map<String, Object> getQueryResultRsqMap() {
            return Objects.isNull(queryResultRsqMap) ? new HashMap<>() : new HashMap<>(queryResultRsqMap);
        }
    }


    /**
     * 是否为V3版本小微升级
     * @param merchantSn 商户号
     * @return
     */
    public boolean isMicroUpgradeV3(String merchantSn) {
        return Objects.equals(businessLicenceTaskService.determineMicroUpgradeVersion(new MicroUpgradeDTO().setMerchantSn(merchantSn)), UpgradeVersion.V3);
    }


    /**
     * 重写calculateMainTaskStatus方法,影响主任务的子任务如果失败,直接将主任务设为失败
     * @param mainTaskDO
     * @param sortedSubTasks
     */
    @Override
    protected void calculateMainTaskStatus(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> sortedSubTasks) {
        // 等待下次继续调度
        long affectMainTaskAlreadySuccessSubNum = sortedSubTasks.stream()
                .filter(t -> Objects.equals(t.getAffectMainTaskStatus(), AffectPrimaryTaskStatusEnum.YES.getValue())
                        && Objects.equals(t.getStatus(), InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS.getValue()))
                .count();
        mainTaskDO.setAlreadySuccessSubTaskNum((int) affectMainTaskAlreadySuccessSubNum);

        // 所有影响主任务状态的子任务都成功
        Set<Integer> affectMainTaskSubTaskStatusSet = sortedSubTasks.stream()
                .filter(t -> Objects.equals(t.getAffectMainTaskStatus(), AffectPrimaryTaskStatusEnum.YES.getValue()))
                .map(InternalScheduleSubTaskDO::getStatus)
                .collect(Collectors.toSet());
        if (affectMainTaskSubTaskStatusSet.contains( InternalScheduleSubTaskStatusEnum.PROCESS_FAIL.getValue())) {
            mainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue());
            log.warn("任务调度失败，存在影响主任务状态的子任务调度失败，主任务id: {}", mainTaskDO.getId());
            if (StringUtils.isBlank(mainTaskDO.getResult())) {
                mainTaskDO.setResult(InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getText());
            }
            return;
        }

        Set<Integer> subTaskStatusSet = sortedSubTasks.stream().map(InternalScheduleSubTaskDO::getStatus).collect(Collectors.toSet());
        HashSet<Integer> needToScheduleStatusSet = Sets.newHashSet(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT.getValue(), InternalScheduleSubTaskStatusEnum.RETRY.getValue(), InternalScheduleSubTaskStatusEnum.WAIT_PROCESS.getValue());
        if (CollectionUtils.containsAny(needToScheduleStatusSet, subTaskStatusSet) && (affectMainTaskAlreadySuccessSubNum != mainTaskDO.getAffectStatusSubTaskNum())) {
            mainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.WAIT_NEXT_SCHEDULED.getValue());
            mainTaskDO.setResult(InternalScheduleMainTaskStatusEnum.WAIT_NEXT_SCHEDULED.getText());
            return;
        }

        // 防止子任务都是不影响主任务状态的
        if ((affectMainTaskSubTaskStatusSet.size() == 1 && affectMainTaskSubTaskStatusSet.contains(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS.getValue()))
                || (subTaskStatusSet.size() == 1 && subTaskStatusSet.contains(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS.getValue()))) {
            mainTaskDO.setResult(InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getText());
            mainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue());
            return;
        }
        if (StringUtils.isBlank(mainTaskDO.getResult())) {
            mainTaskDO.setResult(InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getText());
        }
        mainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue());
    }
}
