package com.wosai.upay.job.handlers;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.ContractTaskTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.LklV3ShopTermBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.validation.In;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 用于处理拉卡拉通道小微升级,其他通道处理参见MicroUpgradeContractSubTaskHandler
 *
 * @Author: zhmh
 * @date: 2025/07/01 18:16
 * @Description:用于处理拉卡拉通道小微升级进件报备
 *
 */
@Component
@Order(90)
@Slf4j
public class MicroUpgradeContractSubTaskHandlerV3 extends AbstractSubTaskHandler {

    @Autowired
    LklV3Service lklV3Service;

    @Autowired
    RuleContext ruleContext;

    @Autowired
    LklV3ShopTermBiz lklV3ShopTermBiz;




 private static final Set<String> SUPPORTED_TASK_TYPES = Sets.newHashSet(
         ContractTaskTypeEnum.NEW_MERCHANT_ONLINE.getValue()

);

@Override
public boolean supports(ContractTask task, ContractSubTask subTask) {
    boolean isChannelLKLV3 = ChannelEnum.LKLV3.getValue().equalsIgnoreCase(subTask.getChannel());
    boolean isPaywayNullOrAcquirer = Objects.isNull(subTask.getPayway())
            || Objects.equals(subTask.getPayway(), PaywayEnum.ACQUIRER.getValue());
    return isChannelLKLV3 && isPaywayNullOrAcquirer && isMicroUpgradeTask(task);
}

    /**
     * 判断主任务是否为小微升级入网任务
     */
    private boolean isMicroUpgradeTask(ContractTask task) {
        return ContractTaskTypeEnum.NEW_MERCHANT_ONLINE.getValue().equals(task.getType())
                && StrUtil.contains(task.getRule_group_id(), ContractRuleConstants.RULE_GROUP_MICROUPGRADE);
    }




    @Override
    protected void handleError(ContractTask task, ContractSubTask subTask, Exception e) {
        handleResult(new ContractResponse().setCode(500).setMessage(e.toString()), subTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doHandle(ContractTask task, ContractSubTask subTask) {
        BasicProvider provider = providerFactory.getProviderByName(subTask.getChannel());
        ContractResponse response = provider.processTaskByRule(task,
                ruleContext.getContractRule(subTask.getContract_rule()).getContractChannel(),
                subTask);
        if (Objects.isNull(response)) {
            log.info("merchantSn {} subTask {} channelName {} processTask return null", task.getMerchant_sn(), subTask.getId(), subTask.getChannel());
            return;
        }
        handleResult(response, subTask);
        if (WosaiStringUtils.isNotEmpty(task.getRule_group_id())
                && (task.getRule_group_id().contains(McConstant.RULE_GROUP_LKLV3) || task.getRule_group_id().contains(McConstant.RULE_GROUP_LKLORG))
                && response.isSuccess()
                && ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(subTask.getTask_type())
        ) {
            Map context = JSON.parseObject(task.getEvent_context(), Map.class);
            Map store = lklV3ShopTermBiz.findFirstStore((String) BeanUtil.getNestedProperty(context, "merchant.id"));
            String storeSn = MapUtils.getString(store, "sn");
            lklV3ShopTermBiz.addMer(task.getMerchant_sn(), storeSn, subTask.getId());
        }
    }


}