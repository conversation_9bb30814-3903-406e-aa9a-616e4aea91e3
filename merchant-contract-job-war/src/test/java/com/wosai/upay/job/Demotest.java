package com.wosai.upay.job;

import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.acquirer.ChangeAcquirerRequest;
import com.wosai.upay.job.refactor.dao.InternalScheduleMainTaskDAO;
import com.wosai.upay.job.refactor.dao.InternalScheduleSubTaskDAO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.service.factory.InternalScheduleTaskFactory;
import com.wosai.upay.job.refactor.task.license.micro.MicroUpgradeV3TaskHandler;
import com.wosai.upay.job.service.AcquirerServiceImpl;
import com.wosai.upay.job.service.BankDirectServiceImpl;
import com.wosai.upay.job.service.BankDirectServiceImplTest;
import com.wosai.upay.job.xxljob.batch.contracttask.ContractTaskIndirectNetInJobHandler;
import com.wosai.upay.job.xxljob.context.ContractTaskContext;
import com.wosai.upay.job.xxljob.direct.bankdirectapply.BankDirectAuthStatusJobHandler;
import com.wosai.upay.job.xxljob.model.BatchExecTypeEnum;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import io.swagger.annotations.Authorization;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/4/16 15:03
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class Demotest  {
    @Autowired
    private BankDirectServiceImpl bankDirectServiceImpl;

    @Autowired
    private CommonEventHandler commonEventHandler;
    @Autowired
    ParamContextBiz paramContextBiz;

    @Autowired
    BankDirectAuthStatusJobHandler bankDirectAuthStatusJobHandler;

    @Autowired
    ContractTaskIndirectNetInJobHandler contractTaskIndirectNetInJobHandler;
    @Autowired
    AcquirerServiceImpl acquirerService;
    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Test
    public void test() {
        bankDirectServiceImpl.restartHXTerminal("**********","TT000IWH");
    }


    @Test
    public void test1() {
        ContractEvent contractEvent = new ContractEvent();
        contractEvent.setMerchant_sn("**************");
        final Map<String, Object> paramContextByMerchantSn = paramContextBiz.getParamContextByMerchantSn("**************");
        String groupId = "lklorg";
        contractEvent.setRule_group_id(groupId);
        contractEvent.setEvent_type(ContractEvent.OPT_TYPE_NET_IN);
        commonEventHandler.handleInsert(contractEvent,paramContextByMerchantSn);
    }


    @Test
    public void test2() {
        final DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setQueryTime(2592000000L);
        directJobParam.setBatchSize(500);
        bankDirectAuthStatusJobHandler.execute(directJobParam);
    }

    @Test
    public void test3() {
//        final BatchJobParam batchJobParam = new BatchJobParam();
//        batchJobParam.setExecType(BatchExecTypeEnum.ASYNC_SERIAL);
//        batchJobParam.setQueryTime(10800000L);
//        batchJobParam.setBatchSize(2000);
//        final List<ContractTaskContext> items = contractTaskIndirectNetInJobHandler.queryTaskItems(batchJobParam);
//        items.forEach(item->{
//            contractTaskIndirectNetInJobHandler.doHandleSingleData(item);
//        });
        final ContractTaskContext contractTaskContext = new ContractTaskContext(contractTaskMapper.selectByPrimaryKey(44291744597L),"");
//        contractTaskIndirectNetInJobHandler.doHandleSingleData(contractTaskContext);
        contractTaskIndirectNetInJobHandler.doHandleSingleData(contractTaskContext);

    }




    @Test
    public void test4() {
        final ChangeAcquirerRequest request = new ChangeAcquirerRequest();
        request.setMerchantSn("21690004091476");
        request.setAcquirer("haike");
        request.setImmediately(Boolean.TRUE);
        request.setTradeAppId("1");
        request.setCancellable(Boolean.FALSE);

        acquirerService.applyChangeAcquirer(request);
    }
    @Resource
    private InternalScheduleTaskFactory internalScheduleTaskFactory;

    @Test
    public void test5() {
//        final BatchJobParam batchJobParam = new BatchJobParam();
//        batchJobParam.setExecType(BatchExecTypeEnum.ASYNC_SERIAL);
//        batchJobParam.setQueryTime(10800000L);
//        batchJobParam.setBatchSize(2000);
//        final List<ContractTaskContext> items = contractTaskIndirectNetInJobHandler.queryTaskItems(batchJobParam);
//        items.forEach(item->{
//            contractTaskIndirectNetInJobHandler.doHandleSingleData(item);
//        });
//        final ContractTaskContext contractTaskContext = new ContractTaskContext(contractTaskMapper.selectByPrimaryKey(44291737710L),"");
//        contractTaskIndirectNetInJobHandler.doHandleSingleData(contractTaskContext);
        internalScheduleTaskFactory.getHandler(InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V3).batchHandleTasksInSequence(3);
    }

    @Resource
    MicroUpgradeV3TaskHandler microUpgradeV3TaskHandler;

    @Resource
    private InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;
    @Resource
    private InternalScheduleSubTaskDAO internalScheduleSubTaskDAO;

    @Test
    public void test6() {
//        microUpgradeV3TaskHandler.doChangePrams(internalScheduleMainTaskDAO.getByPrimaryKey(20735L).get(),
//                internalScheduleSubTaskDAO.getByPrimaryKey(21998L).get());
    }

    @Test
    public void test7() {
       microUpgradeV3TaskHandler.handleReContractSubTask(internalScheduleMainTaskDAO.getByPrimaryKey(20912L).get(),
               internalScheduleSubTaskDAO.getByPrimaryKey(22207L).get());


    }
}
