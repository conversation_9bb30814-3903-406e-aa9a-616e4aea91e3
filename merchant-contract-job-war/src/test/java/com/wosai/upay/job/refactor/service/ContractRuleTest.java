package com.wosai.upay.job.refactor.service;

import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.service.impl.McRulesDecisionServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Iterator;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 规则测试类
 *
 * <AUTHOR>
 * @date 2023/11/29 16:59
 */
@Slf4j
@ActiveProfiles("dev")
public class ContractRuleTest extends BaseTest {

    @Resource
    private McRulesDecisionServiceImpl mcRulesDecisionService;


    private final MerchantFeatureBO merchantFeatureBO = new MerchantFeatureBO();

    @Before
    public void initMerchant() {
        merchantFeatureBO.setMerchantSn("test001");
        MerchantFeatureBO.ExtraFeature extraFeature = merchantFeatureBO.new ExtraFeature();
        MerchantFeatureBO.ExtraFeature.AccountInfo accountInfo = extraFeature.new AccountInfo();
        accountInfo.setAccountType(1);
        accountInfo.setIdentityId("540127198108113435");
        accountInfo.setHolderName("齐天大圣");
        MerchantBusinessLicenseInfo merchantBusinessLicenseInfo = new MerchantBusinessLicenseInfo();
        merchantBusinessLicenseInfo.setLegal_person_id_number("540127198108113435");
        merchantBusinessLicenseInfo.setName("");
        merchantBusinessLicenseInfo.setType(1);
        extraFeature.setAccountInfo(accountInfo);
        extraFeature.setMerchantBusinessLicenseInfo(merchantBusinessLicenseInfo);
        merchantFeatureBO.setExtraFeature(extraFeature);
    }

    @Test
    public void tesRulesIndustry() {
        merchantFeatureBO.setType("0");
        merchantFeatureBO.setLegalPersonType("1");
        merchantFeatureBO.setSettlementAccountType("1");
        MerchantFeatureBO.ExtraFeature extraFeature = merchantFeatureBO.getExtraFeature();
        MerchantBusinessLicenseInfo licenseInfo = extraFeature.getMerchantBusinessLicenseInfo();
        licenseInfo.setType(0);
        // 特殊底价行业不能进富友
        merchantFeatureBO.setIndustry("0960b7eb-4449-4a52-a899-fc7f92ec4e87");
        mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().forEach(groupCombinedStrategyDetailDO -> {
            assertThat(groupCombinedStrategyDetailDO.getGroupId()).isNotEqualTo("fuyou");
        });
    }


    /**
     * 省选择规则,排除一些市,除了大客户组织,主海科备拉卡拉
     */
    @Test
    public void test1() {
        merchantFeatureBO.setProvinceCode("440000");
        merchantFeatureBO.setPromotionOrganizationPath("7777777");
        mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        Iterator<GroupCombinedStrategyDetailDO> iterator = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        iterator.next().getGroupId().equals("haike");
        iterator.next().getGroupId().equals("lklorg");

    }


    /**
     * 省选择规则,排除一些市,大客户组织,主拉卡拉备海科
     */
    @Test
    public void test2() {
        merchantFeatureBO.setProvinceCode("440000");
        merchantFeatureBO.setPromotionOrganizationPath("00069");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("lklorg");
        assertThat( iterator1.next().getGroupId()).isEqualTo("haike");
    }

    /**
     * 市选择规则,小微个体或者企业组织对公,除了大客户组织,行业合作,主富有备拉卡拉
     */
    @Test
    public void test3() {
        merchantFeatureBO.setCityCode("440900");
        merchantFeatureBO.setPromotionOrganizationPath("333");
        merchantFeatureBO.setType("1");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("fuyou");
        assertThat( iterator1.next().getGroupId()).isEqualTo("lklorg");
    }


    /**
     * 市选择规则,小微个体或者企业组织对公,大客户组织,行业合作,拉卡拉
     */
    @Test
    public void test4() {
        merchantFeatureBO.setCityCode("440900");
        merchantFeatureBO.setPromotionOrganizationPath("00069");
        merchantFeatureBO.setType("1");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("lklorg");
        assertThat(iterator1.hasNext()).isFalse();
    }

    /**
     * 市选择规则,企业组织对私,拉卡拉
     */
    @Test
    public void test5() {
        merchantFeatureBO.setProvinceCode("440900");
        merchantFeatureBO.setPromotionOrganizationPath("00069");
        merchantFeatureBO.setType("3");
        merchantFeatureBO.setBankAccountType("1");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("lklorg");
        assertThat(iterator1.hasNext()).isFalse();
    }


    /**
     * 省选择规则,小微个体或者企业组织对公,推广组织非大客户组织,主富友备拉卡拉
     */
    @Test
    public void test6() {
        merchantFeatureBO.setProvinceCode("320000");
        merchantFeatureBO.setPromotionOrganizationPath("555");
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setBankAccountType("1");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("fuyou");
        assertThat( iterator1.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * 省选择规则,小微个体或者企业组织对公,推广组织是大客户组织,主拉卡拉备海科
     */
    @Test
    public void test7() {
        merchantFeatureBO.setProvinceCode("320000");
        merchantFeatureBO.setPromotionOrganizationPath("00069");
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setBankAccountType("1");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("lklorg");
        assertThat( iterator1.next().getGroupId()).isEqualTo("haike");
    }

    /**
     * 省选择规则,企业组织对私,推广组织非大客户组织,主海科备拉卡拉
     */
    @Test
    public void test8() {
        merchantFeatureBO.setProvinceCode("320000");
        merchantFeatureBO.setPromotionOrganizationPath("3333");
        merchantFeatureBO.setType("3");
        merchantFeatureBO.setBankAccountType("1");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("haike");
        assertThat( iterator1.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * 省选择规则,企业组织对私,推广组织大客户组织,主拉卡拉备海科
     */
    @Test
    public void test9() {
        merchantFeatureBO.setProvinceCode("320000");
        merchantFeatureBO.setPromotionOrganizationPath("00069");
        merchantFeatureBO.setType("3");
        merchantFeatureBO.setBankAccountType("1");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("lklorg");
        assertThat( iterator1.next().getGroupId()).isEqualTo("haike");
    }

    /**
     * 省选择规则,小微个体或者企业组织对公,除了大客户组织,行业合作,主富友备拉卡拉
     */
    @Test
    public void test10() {
        merchantFeatureBO.setProvinceCode("510000");
        merchantFeatureBO.setPromotionOrganizationPath("555");
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setBankAccountType("1");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("fuyou");
        assertThat( iterator1.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * 省选择规则,小微个体或者企业组织对公,大客户组织,行业合作,主海科备拉卡拉
     */
    @Test
    public void test11() {
        merchantFeatureBO.setProvinceCode("210000");
        merchantFeatureBO.setPromotionOrganizationPath("00069");
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setBankAccountType("1");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("haike");
        assertThat( iterator1.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * 省选择规则,企业组织对私,主海科备拉卡拉
     */
    @Test
    public void test12() {
        merchantFeatureBO.setProvinceCode("460000");
        merchantFeatureBO.setPromotionOrganizationPath("00069");
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setBankAccountType("1");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("haike");
        assertThat( iterator1.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * 省选择规则 主海科备拉卡拉
     */
    @Test
    public void test13() {
        merchantFeatureBO.setProvinceCode("350000");
        merchantFeatureBO.setPromotionOrganizationPath("00069");
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setBankAccountType("1");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("haike");
        assertThat( iterator1.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * 省选择规则,小微个体或者企业组织对公,除了大客户组织,行业合作,主富友备海科
     */
    @Test
    public void test14() {
        merchantFeatureBO.setProvinceCode("230000");
        merchantFeatureBO.setPromotionOrganizationPath("34234");
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setBankAccountType("1");
        Iterator<GroupCombinedStrategyDetailDO> iterator1 = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1().iterator();
        assertThat( iterator1.next().getGroupId()).isEqualTo("fuyou");
        assertThat( iterator1.next().getGroupId()).isEqualTo("haike");
    }
}
